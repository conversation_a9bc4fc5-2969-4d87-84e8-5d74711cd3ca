'use server';

import { Prisma } from '@prisma/client';
import { revalidatePath } from 'next/cache';

import prisma from '@/lib/prisma';

import { createDebtorSchema, updateDebtorSchema } from './schemas';

import type {
  CreateDebtorData,
  UpdateDebtorData,
  DebtorWithRelations,
} from './types';
import type { ActionResult } from '@/lib/types';
import type { Debtor as PrismaDebtor } from '@prisma/client';

const convertDecimalToNumber = (
  decimal: Prisma.Decimal | null | undefined,
): number => {
  if (!decimal) return 0;
  return parseFloat(decimal.toString());
};

export async function getDebtors(): Promise<DebtorWithRelations[]> {
  try {
    const debtors = await prisma.debtor.findMany({
      include: {
        cases: true,
        debts: {
          include: {
            creditor: {
              select: {
                name: true,
              },
            },
          },
        },
        assets: true,
        _count: {
          select: {
            cases: true,
            debts: true,
            assets: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    return debtors.map((debtor) => ({
      id: debtor.id,
      name: debtor.name,
      idNumber: debtor.idNumber,
      idType: debtor.idType,
      email: debtor.email,
      phone: debtor.phone,
      address: debtor.address,
      city: debtor.city,
      department: debtor.department,
      birthDate: debtor.birthDate,
      maritalStatus: debtor.maritalStatus,
      occupation: debtor.occupation,
      monthlyIncome: convertDecimalToNumber(debtor.monthlyIncome),
      monthlyExpenses: convertDecimalToNumber(debtor.monthlyExpenses),
      dependents: debtor.dependents,
      educationLevel: debtor.educationLevel,
      totalDebt: convertDecimalToNumber(debtor.totalDebt),
      status: debtor.status,
      emergencyContact: debtor.emergencyContact,
      emergencyPhone: debtor.emergencyPhone,
      bankAccount: debtor.bankAccount,
      bankName: debtor.bankName,
      accountType: debtor.accountType,
      description: debtor.description,
      activeCases: debtor.activeCases,
      createdDate: debtor.createdDate,
      lastUpdate: debtor.lastUpdate,
      cases: debtor.cases.map((caseItem) => ({
        id: caseItem.id,
        caseNumber: caseItem.caseNumber,
        debtorName: caseItem.debtorName,
        type: caseItem.type,
        status: caseItem.status,
        totalDebt: convertDecimalToNumber(caseItem.totalDebt),
        creditors: caseItem.creditors,
        createdDate: caseItem.createdDate,
        hearingDate: caseItem.hearingDate,
        phase: caseItem.phase,
        causes: caseItem.causes || [],
        debtorId: caseItem.debtorId,
        operatorId: caseItem.operatorId,
      })),
      debts: debtor.debts.map((debt) => ({
        id: debt.id,
        amount: convertDecimalToNumber(debt.amount),
        interestRate: debt.interestRate,
        type: debt.type,
        caseId: debt.caseId,
        creditorId: debt.creditorId,
        debtorId: debt.debtorId,
        creditor: debt.creditor,
      })),
      assets: debtor.assets.map((asset) => ({
        id: asset.id,
        name: asset.name,
        type: asset.type,
        value: convertDecimalToNumber(asset.value),
        caseId: asset.caseId,
        debtorId: asset.debtorId,
      })),
      _count: debtor._count,
    }));
  } catch (error) {
    console.error('Error fetching debtors:', error);
    throw new Error('Error al obtener los deudores');
  }
}

export async function getDebtorById(id: string): Promise<DebtorWithRelations> {
  try {
    if (!id) {
      throw new Error('ID del deudor es requerido');
    }

    const debtor = await prisma.debtor.findUnique({
      where: { id },
      include: {
        cases: true,
        debts: {
          include: {
            creditor: {
              select: {
                name: true,
              },
            },
          },
        },
        assets: true,
        _count: {
          select: {
            cases: true,
            debts: true,
            assets: true,
          },
        },
      },
    });

    if (!debtor) {
      throw new Error('Deudor no encontrado');
    }

    return {
      id: debtor.id,
      name: debtor.name,
      idNumber: debtor.idNumber,
      idType: debtor.idType,
      email: debtor.email,
      phone: debtor.phone,
      address: debtor.address,
      city: debtor.city,
      department: debtor.department,
      birthDate: debtor.birthDate,
      maritalStatus: debtor.maritalStatus,
      occupation: debtor.occupation,
      monthlyIncome: convertDecimalToNumber(debtor.monthlyIncome),
      monthlyExpenses: convertDecimalToNumber(debtor.monthlyExpenses),
      dependents: debtor.dependents,
      educationLevel: debtor.educationLevel,
      totalDebt: convertDecimalToNumber(debtor.totalDebt),
      status: debtor.status,
      emergencyContact: debtor.emergencyContact,
      emergencyPhone: debtor.emergencyPhone,
      bankAccount: debtor.bankAccount,
      bankName: debtor.bankName,
      accountType: debtor.accountType,
      description: debtor.description,
      activeCases: debtor.activeCases,
      createdDate: debtor.createdDate,
      lastUpdate: debtor.lastUpdate,
      cases: debtor.cases.map((caseItem) => ({
        id: caseItem.id,
        caseNumber: caseItem.caseNumber,
        debtorName: caseItem.debtorName,
        type: caseItem.type,
        status: caseItem.status,
        totalDebt: convertDecimalToNumber(caseItem.totalDebt),
        creditors: caseItem.creditors,
        createdDate: caseItem.createdDate,
        hearingDate: caseItem.hearingDate,
        phase: caseItem.phase,
        causes: caseItem.causes || [],
        debtorId: caseItem.debtorId,
        operatorId: caseItem.operatorId,
      })),
      debts: debtor.debts.map((debt) => ({
        id: debt.id,
        amount: convertDecimalToNumber(debt.amount),
        interestRate: debt.interestRate,
        type: debt.type,
        caseId: debt.caseId,
        creditorId: debt.creditorId,
        debtorId: debt.debtorId,
        creditor: debt.creditor,
      })),
      assets: debtor.assets.map((asset) => ({
        id: asset.id,
        name: asset.name,
        type: asset.type,
        value: convertDecimalToNumber(asset.value),
        caseId: asset.caseId,
        debtorId: asset.debtorId,
      })),
      _count: debtor._count,
    };
  } catch (error) {
    console.error('Error fetching debtor:', error);
    throw new Error('Error al obtener el deudor');
  }
}

export async function createDebtor(
  values: CreateDebtorData,
): Promise<ActionResult<PrismaDebtor>> {
  const { success, data, error } = createDebtorSchema.safeParse(values);

  if (!success) {
    return {
      success: false,
      message: 'Por favor complete todos los campos requeridos',
      errors: error.flatten().fieldErrors,
    };
  }

  try {
    const debtor = await prisma.debtor.create({
      data: {
        ...data,
        status: 'Activo',
        totalDebt: 0,
        activeCases: 0,
      },
      include: {
        cases: true,
        debts: {
          include: {
            creditor: {
              select: {
                name: true,
              },
            },
          },
        },
        assets: true,
        _count: {
          select: {
            cases: true,
            debts: true,
            assets: true,
          },
        },
      },
    });

    revalidatePath('/debtors');

    return {
      success: true,
      message: 'Deudor creado exitosamente',
      data: {
        ...debtor,
        monthlyIncome: convertDecimalToNumber(debtor.monthlyIncome),
        monthlyExpenses: convertDecimalToNumber(debtor.monthlyExpenses),
        totalDebt: convertDecimalToNumber(debtor.totalDebt),
      } as any,
    };
  } catch (error) {
    console.error('Error creating debtor:', error);

    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2002') {
        const target = error.meta?.target as string[];
        if (target?.includes('email')) {
          return {
            success: false,
            message:
              'Ya existe un deudor registrado con este correo electrónico',
          };
        }
        if (target?.includes('idNumber')) {
          return {
            success: false,
            message:
              'Ya existe un deudor registrado con este número de documento',
          };
        }
      }
    }

    return {
      success: false,
      message: 'Error al crear el deudor',
    };
  }
}

export async function updateDebtor(
  values: UpdateDebtorData,
): Promise<ActionResult<PrismaDebtor>> {
  console.log('updateDebtor called with values:', values);
  const { success, data, error } = updateDebtorSchema.safeParse(values);

  if (!success) {
    console.log('Validation failed:', error.flatten().fieldErrors);
    return {
      success: false,
      message: 'Por favor complete todos los campos requeridos',
      errors: error.flatten().fieldErrors,
    };
  }

  try {
    const updatedDebtor = await prisma.debtor.update({
      where: { id: data.id },
      data,
      include: {
        cases: true,
        debts: {
          include: {
            creditor: {
              select: {
                name: true,
              },
            },
          },
        },
        assets: true,
        _count: {
          select: {
            cases: true,
            debts: true,
            assets: true,
          },
        },
      },
    });

    revalidatePath('/debtors');

    return {
      success: true,
      message: 'Deudor actualizado exitosamente',
      data: {
        ...updatedDebtor,
        monthlyIncome: convertDecimalToNumber(updatedDebtor.monthlyIncome),
        monthlyExpenses: convertDecimalToNumber(updatedDebtor.monthlyExpenses),
        totalDebt: convertDecimalToNumber(updatedDebtor.totalDebt),
      } as any,
    };
  } catch (error) {
    console.error('Error updating debtor:', error);

    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2002') {
        const target = error.meta?.target as string[];
        if (target?.includes('email')) {
          return {
            success: false,
            message:
              'Ya existe un deudor registrado con este correo electrónico',
          };
        }
        if (target?.includes('idNumber')) {
          return {
            success: false,
            message:
              'Ya existe un deudor registrado con este número de documento',
          };
        }
      }
    }

    return {
      success: false,
      message: 'Error al actualizar el deudor',
    };
  }
}

export async function deleteDebtor(
  id: string,
): Promise<ActionResult<PrismaDebtor>> {
  if (!id) {
    return {
      success: false,
      message: 'ID del deudor es requerido',
    };
  }

  try {
    const deletedDebtor = await prisma.debtor.delete({
      where: { id },
    });

    revalidatePath('/debtors');

    return {
      success: true,
      message: 'Deudor eliminado exitosamente',
      data: deletedDebtor,
    };
  } catch (error) {
    console.error('Error deleting debtor:', error);
    return {
      success: false,
      message: 'Error al eliminar el deudor',
    };
  }
}

export async function toggleDebtorStatus(
  id: string,
): Promise<ActionResult<PrismaDebtor>> {
  try {
    if (!id) {
      throw new Error('ID del deudor es requerido');
    }

    const debtor = await prisma.debtor.findUnique({
      where: { id },
      select: { status: true },
    });

    if (!debtor) {
      throw new Error('Deudor no encontrado');
    }

    const newStatus = debtor.status === 'Activo' ? 'Inactivo' : 'Activo';

    const updatedDebtor = await prisma.debtor.update({
      where: { id },
      data: { status: newStatus },
    });

    revalidatePath('/debtors');

    return {
      success: true,
      message: `Estado del deudor actualizado a ${newStatus}`,
      data: updatedDebtor,
    };
  } catch (error) {
    console.error('Error toggling debtor status:', error);
    return {
      success: false,
      message: 'Error al cambiar el estado del deudor',
    };
  }
}
