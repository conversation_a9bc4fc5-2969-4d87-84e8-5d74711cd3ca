import { z } from 'zod';

export const createCaseSchema = z.object({
  debtorId: z.string().min(1, 'El deudor es requerido'),
  type: z.enum(['INSOLVENCY', 'CONCILIATION', 'SUPPORT_AGREEMENT'], {
    errorMap: () => ({ message: 'Tipo de caso inválido' }),
  }),
  status: z.string().min(1, 'El estado es requerido'),
  totalDebt: z.number().min(0, 'La deuda total debe ser mayor a 0'),
  creditors: z.number().min(1, 'Debe haber al menos un acreedor'),
  hearingDate: z.string().optional().nullable(),
  phase: z.string().optional(),
  operatorId: z.string().min(1, 'El operador es requerido'),
  caseNumber: z.string().optional(),
  causes: z.array(z.string()).default([]),
});

export const updateCaseSchema = z.object({
  id: z.string().min(1, 'El ID es requerido'),
  debtorId: z.string().min(1, 'El deudor es requerido').optional(),
  type: z
    .enum(['INSOLVENCY', 'CONCILIATION', 'SUPPORT_AGREEMENT'], {
      errorMap: () => ({ message: 'Tipo de caso inválido' }),
    })
    .optional(),
  status: z.string().optional(),
  totalDebt: z.number().min(0, 'La deuda total debe ser mayor a 0').optional(),
  creditors: z.number().min(1, 'Debe haber al menos un acreedor').optional(),
  hearingDate: z.string().optional().nullable(),
  phase: z.string().optional(),
  operatorId: z.string().optional(),
  causes: z.array(z.string()).optional(),
});

export type CreateCaseData = z.infer<typeof createCaseSchema>;
export type UpdateCaseData = z.infer<typeof updateCaseSchema>;
