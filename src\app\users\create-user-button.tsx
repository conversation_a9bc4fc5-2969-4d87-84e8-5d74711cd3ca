'use client';

import { Plus } from 'lucide-react';
import { useState, useTransition } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { createUser } from '@/features/user/actions';

import { CreateUserDialog } from './components/create-user-dialog';

import type { UIRole } from '@/features/user/types';

interface CreateUserButtonProps {
  roles: UIRole[];
  onUserCreated?: () => void;
}

export function CreateUserButton({
  roles,
  onUserCreated,
}: Readonly<CreateUserButtonProps>) {
  const [showDialog, setShowDialog] = useState(false);
  const [isPending, startTransition] = useTransition();

  const handleCreateUser = (userData: {
    name: string;
    email: string;
    phone: string;
    address: string;
    professionalCard: string;
    roleId: string;
    status: string;
    password?: string;
    sendWelcomeEmail: boolean;
  }) => {
    startTransition(async () => {
      const formData = new FormData();
      formData.append('name', userData.name);
      formData.append('email', userData.email);
      formData.append('phone', userData.phone);
      formData.append('address', userData.address);
      formData.append('professionalCard', userData.professionalCard);
      formData.append('roleId', userData.roleId);
      formData.append('status', userData.status);
      if (userData.password) {
        formData.append('password', userData.password);
      }

      const result = await createUser({
        name: userData.name,
        email: userData.email,
        phone: userData.phone,
        address: userData.address,
        professionalCard: userData.professionalCard,
        roleId: userData.roleId,
        status: userData.status,
        password: userData.password ?? '',
      });

      if (result.success) {
        toast.success(result.message);
        setShowDialog(false);
        onUserCreated?.();
      } else {
        toast.error(result.message);
      }
    });
  };

  return (
    <>
      <Button onClick={() => setShowDialog(true)} disabled={isPending}>
        <Plus className="mr-2 h-4 w-4" />
        Nuevo Usuario
      </Button>
      <CreateUserDialog
        open={showDialog}
        onOpenChange={setShowDialog}
        onCreateUser={handleCreateUser}
        roles={roles}
      />
    </>
  );
}
