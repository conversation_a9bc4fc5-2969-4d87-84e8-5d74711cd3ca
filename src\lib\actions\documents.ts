import { revalidatePath } from 'next/cache';

import {
  createDocumentSchema,
  updateDocumentSchema,
} from '@/features/document/schemas';
import prisma from '@/lib/prisma';

import type {
  CreateDocumentData,
  UpdateDocumentData,
} from '@/features/document/schemas';
import type { ActionResult } from '@/lib/types';
import type { Document as PrismaDocument, Prisma } from '@prisma/client';

export interface DocumentFilter {
  caseId?: string;
  type?: string;
  status?: string;
  search?: string;
}

export interface Document {
  id: string;
  name: string;
  type: string;
  status: string;
  uploadDate?: string;
  url?: string;
  caseId?: string;
  debtorName?: string;
  category?: string;
  createdDate?: string;
  size?: string;
  format?: string;
  createdBy?: string;
  downloadCount?: number;
  lastAccessed?: string;
  content?: string;
  viewCount?: number;
  shareCount?: number;
}

type DocumentWithCase = Prisma.DocumentGetPayload<{
  include: {
    case: {
      select: {
        id: true;
        caseNumber: true;
        debtorName: true;
        type: true;
        status: true;
      };
    };
  };
}>;

const mapDocumentTypeToSpanish = (type: string): string => {
  const typeMap: Record<string, string> = {
    CEDULA: 'Cédula',
    RUT: 'RUT',
    ESTADOS_FINANCIEROS: 'Estados Financieros',
    CERTIFICADO_INGRESOS: 'Certificado de Ingresos',
    ESCRITURA_PUBLICA: 'Escritura Pública',
    AUTORIZACION: 'Autorización',
    DEMANDA: 'Demanda',
    RESPUESTA_DEMANDA: 'Respuesta a Demanda',
    ACUERDO: 'Acuerdo',
    SENTENCIA: 'Sentencia',
    OTRO: 'Otro',
  };
  return typeMap[type] || type;
};

const mapDocumentStatusToSpanish = (status: string): string => {
  const statusMap: Record<string, string> = {
    PENDIENTE: 'Pendiente',
    APROBADO: 'Aprobado',
    RECHAZADO: 'Rechazado',
    EN_REVISION: 'En Revisión',
  };
  return statusMap[status] || status;
};

const mapCaseTypeToSpanish = (type: string): string => {
  const typeMap: Record<string, string> = {
    INSOLVENCY: 'Insolvencia',
    CONCILIATION: 'Conciliación',
    SUPPORT_AGREEMENT: 'Acuerdo de Apoyo',
  };
  return typeMap[type] || type;
};

const mapCaseStatusToSpanish = (status: string): string => {
  const statusMap: Record<string, string> = {
    NEGOTIATION: 'En negociación',
    HEARING_SCHEDULED: 'Audiencia programada',
    PENDING_DOCUMENTS: 'Documentos pendientes',
    AGREEMENT_APPROVED: 'Acuerdo aprobado',
    CLOSED: 'Cerrado',
  };
  return statusMap[status] || status;
};

export async function getDocuments(filters?: DocumentFilter) {
  try {
    const where: Prisma.DocumentWhereInput = {};

    if (filters?.caseId) {
      where.caseId = filters.caseId;
    }

    if (filters?.type) {
      where.type = filters.type;
    }

    if (filters?.status) {
      where.status = filters.status;
    }

    if (filters?.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        {
          case: {
            caseNumber: { contains: filters.search, mode: 'insensitive' },
          },
        },
        {
          case: {
            debtorName: { contains: filters.search, mode: 'insensitive' },
          },
        },
      ];
    }

    const documents = await prisma.document.findMany({
      where,
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
      orderBy: {
        uploadDate: 'desc',
      },
    });

    return documents.map((document: DocumentWithCase) => ({
      ...document,
      type: mapDocumentTypeToSpanish(document.type),
      status: mapDocumentStatusToSpanish(document.status),
      case: {
        ...document.case,
        type: mapCaseTypeToSpanish(document.case.type),
        status: mapCaseStatusToSpanish(document.case.status),
      },
    }));
  } catch (error) {
    console.error('Error fetching documents:', error);
    throw new Error('Error al obtener los documentos');
  }
}

export async function getDocumentById(id: string) {
  try {
    if (!id) {
      throw new Error('ID del documento es requerido');
    }

    const document = await prisma.document.findUnique({
      where: { id },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    if (!document) {
      throw new Error('Documento no encontrado');
    }

    return {
      ...document,
      type: mapDocumentTypeToSpanish(document.type),
      status: mapDocumentStatusToSpanish(document.status),
      case: {
        ...document.case,
        type: mapCaseTypeToSpanish(document.case.type),
        status: mapCaseStatusToSpanish(document.case.status),
      },
    };
  } catch (error) {
    console.error('Error fetching document:', error);
    throw new Error('Error al obtener el documento');
  }
}

export async function createDocument(
  values: CreateDocumentData,
): Promise<ActionResult<PrismaDocument>> {
  'use server';

  const { success, data, error } = createDocumentSchema.safeParse(values);

  if (!success) {
    return {
      success: false,
      message: 'Por favor complete todos los campos requeridos',
      errors: error.flatten().fieldErrors,
    };
  }

  try {
    const document = await prisma.document.create({
      data: {
        ...data,
        status: data.status ?? 'PENDIENTE',
      },
      include: {
        case: {
          select: {
            id: true,
            caseNumber: true,
            debtorName: true,
            type: true,
            status: true,
          },
        },
      },
    });

    revalidatePath('/documents');

    return {
      success: true,
      message: 'Documento creado exitosamente',
      data: document,
    };
  } catch (error) {
    console.error('Error creating document:', error);

    if (error instanceof Error) {
      return {
        success: false,
        message: error.message,
      };
    }

    return {
      success: false,
      message: 'Error al crear el documento',
    };
  }
}

export async function updateDocument(
  values: UpdateDocumentData,
): Promise<ActionResult<PrismaDocument>> {
  'use server';

  const { success, data, error } = updateDocumentSchema.safeParse(values);

  if (!success) {
    return {
      success: false,
      message: 'Por favor complete todos los campos requeridos',
      errors: error.flatten().fieldErrors,
    };
  }

  const { id } = data;
  if (!id) {
    return {
      success: false,
      message: 'ID del documento es requerido',
    };
  }

  try {
    const { id: documentId, ...updateData } = data;
    const document = await prisma.document.update({
      where: { id: documentId },
      data: updateData,
    });

    revalidatePath('/documents');

    return {
      success: true,
      message: 'Documento actualizado exitosamente',
      data: document,
    };
  } catch (error) {
    console.error('Error updating document:', error);
    return {
      success: false,
      message: 'Error al actualizar el documento',
    };
  }
}

export async function deleteDocument(
  id: string,
): Promise<ActionResult<PrismaDocument>> {
  'use server';

  if (!id) {
    return {
      success: false,
      message: 'ID del documento es requerido',
    };
  }

  try {
    const deletedDocument = await prisma.document.delete({
      where: { id },
    });

    revalidatePath('/documents');

    return {
      success: true,
      message: 'Documento eliminado exitosamente',
      data: deletedDocument,
    };
  } catch (error) {
    console.error('Error deleting document:', error);
    return {
      success: false,
      message: 'Error al eliminar el documento',
    };
  }
}

export async function getDocumentStats() {
  try {
    const [total, byStatus, byType] = await Promise.all([
      prisma.document.count(),
      prisma.document.groupBy({
        by: ['status'],
        _count: {
          id: true,
        },
      }),
      prisma.document.groupBy({
        by: ['type'],
        _count: {
          id: true,
        },
      }),
    ]);

    return {
      total,
      byStatus,
      byType,
    };
  } catch (error) {
    console.error('Error fetching document stats:', error);
    throw new Error('Error al obtener las estadísticas de documentos');
  }
}
