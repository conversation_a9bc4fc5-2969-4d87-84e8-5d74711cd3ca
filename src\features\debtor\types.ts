import type { createDebtorSchema, updateDebtorSchema } from './schemas';
import type { Prisma } from '@prisma/client';
import type { z } from 'zod';

type DebtorWithRelationsRaw = Prisma.DebtorGetPayload<{
  include: {
    cases: true;
    debts: {
      include: {
        creditor: {
          select: {
            name: true;
          };
        };
      };
    };
    assets: true;
    _count: {
      select: {
        cases: true;
        debts: true;
        assets: true;
      };
    };
  };
}>;

export interface DebtorWithRelations
  extends Omit<
    DebtorWithRelationsRaw,
    | 'monthlyIncome'
    | 'monthlyExpenses'
    | 'totalDebt'
    | 'debts'
    | 'assets'
    | 'cases'
  > {
  monthlyIncome: number;
  monthlyExpenses: number | null;
  totalDebt: number;
  debts: (Omit<DebtorWithRelationsRaw['debts'][0], 'amount'> & {
    amount: number;
  })[];
  assets: (Omit<DebtorWithRelationsRaw['assets'][0], 'value'> & {
    value: number;
  })[];
  cases: (Omit<DebtorWithRelationsRaw['cases'][0], 'totalDebt'> & {
    totalDebt: number;
  })[];
}

export interface UIDebtorWithRelations
  extends Omit<
    DebtorWithRelations,
    'createdDate' | 'lastUpdate' | 'birthDate'
  > {
  createdDate: string;
  lastUpdate: string;
  birthDate: string | null;
}

export interface DebtorStats {
  total: number;
  active: number;
  inactive: number;
  totalDebt: number;
  averageDebt: number;
  byStatus: {
    status: string;
    count: number;
  }[];
  byCity: {
    city: string;
    count: number;
  }[];
}

export type CreateDebtorData = z.infer<typeof createDebtorSchema>;
export type UpdateDebtorData = z.infer<typeof updateDebtorSchema>;
