'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { User, MapPin, CreditCard } from 'lucide-react';
import { useEffect, useTransition } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { updateDebtor } from '@/features/debtor/actions';
import { updateDebtorSchema } from '@/features/debtor/schemas';
import { parseCurrencyInput } from '@/lib/utils';

import type {
  UpdateDebtorData,
  UIDebtorWithRelations,
} from '@/features/debtor/types';

interface EditDebtorDialogProps {
  debtor: UIDebtorWithRelations | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditDebtorDialog({
  debtor,
  open,
  onOpenChange,
}: Readonly<EditDebtorDialogProps>) {
  const [isPending, startTransition] = useTransition();

  const form = useForm<UpdateDebtorData>({
    resolver: zodResolver(updateDebtorSchema),
    defaultValues: {
      name: '',
      idNumber: '',
      idType: 'CC',
      email: '',
      phone: '',
      address: '',
      city: '',
      department: '',
      occupation: '',
      monthlyIncome: 0,
      monthlyExpenses: 0,
      dependents: 0,
      maritalStatus: '',
      educationLevel: '',
      emergencyContact: '',
      emergencyPhone: '',
      bankAccount: '',
      bankName: '',
      accountType: 'AHORROS',
      description: '',
    },
  });

  useEffect(() => {
    if (debtor) {
      form.reset({
        name: debtor.name,
        idNumber: debtor.idNumber,
        idType: debtor.idType,
        email: debtor.email,
        phone: debtor.phone,
        address: debtor.address,
        city: debtor.city,
        department: debtor.department,
        occupation: debtor.occupation,
        monthlyIncome: debtor.monthlyIncome,
        monthlyExpenses: debtor.monthlyExpenses ?? 0,
        dependents: debtor.dependents ?? 0,
        maritalStatus: debtor.maritalStatus ?? '',
        educationLevel: debtor.educationLevel ?? '',
        emergencyContact: debtor.emergencyContact ?? '',
        emergencyPhone: debtor.emergencyPhone ?? '',
        bankAccount: debtor.bankAccount ?? '',
        bankName: debtor.bankName ?? '',
        accountType: debtor.accountType ?? 'AHORROS',
        description: debtor.description ?? '',
      });
    }
  }, [debtor, form]);

  const onSubmit = (data: UpdateDebtorData) => {
    if (!debtor) return;

    startTransition(async () => {
      const result = await updateDebtor({
        ...data,
        id: debtor.id,
      });

      if (result.success) {
        toast.success(result.message);
        onOpenChange(false);
        window.location.reload();
      } else {
        toast.error(result.message);
      }
    });
  };

  if (!debtor) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-5xl">
        <DialogHeader>
          <DialogTitle>Editar Deudor</DialogTitle>
          <DialogDescription>
            Actualice la información personal y financiera del deudor
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={(e) => void form.handleSubmit(onSubmit)(e)}>
            <Tabs defaultValue="personal" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="personal">Personal</TabsTrigger>
                <TabsTrigger value="financial">Financiero</TabsTrigger>
                <TabsTrigger value="contact">Contacto</TabsTrigger>
              </TabsList>

              <TabsContent value="personal" className="mt-4 space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center text-lg">
                      <User className="mr-2 h-5 w-5" />
                      Información Personal
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem className="col-span-2">
                          <FormLabel>Nombre Completo</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="idType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tipo de Documento</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="CC">
                                Cédula de Ciudadanía
                              </SelectItem>
                              <SelectItem value="CE">
                                Cédula de Extranjería
                              </SelectItem>
                              <SelectItem value="PA">Pasaporte</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="idNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Número de Documento</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="maritalStatus"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Estado Civil</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="SOLTERO">
                                Soltero(a)
                              </SelectItem>
                              <SelectItem value="CASADO">Casado(a)</SelectItem>
                              <SelectItem value="UNION_LIBRE">
                                Unión Libre
                              </SelectItem>
                              <SelectItem value="DIVORCIADO">
                                Divorciado(a)
                              </SelectItem>
                              <SelectItem value="VIUDO">Viudo(a)</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dependents"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Personas a Cargo</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              {...field}
                              onChange={(e) =>
                                field.onChange(parseInt(e.target.value) || 0)
                              }
                              value={field.value ?? 0}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="financial" className="mt-4 space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center text-lg">
                      <CreditCard className="mr-2 h-5 w-5" />
                      Información Financiera
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="occupation"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Ocupación</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="educationLevel"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Nivel Educativo</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="PRIMARIA">Primaria</SelectItem>
                              <SelectItem value="SECUNDARIA">
                                Secundaria
                              </SelectItem>
                              <SelectItem value="TECNICO">Técnico</SelectItem>
                              <SelectItem value="UNIVERSITARIO">
                                Universitario
                              </SelectItem>
                              <SelectItem value="POSTGRADO">
                                Postgrado
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="monthlyIncome"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Ingresos Mensuales</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <span className="absolute top-1/2 left-3 -translate-y-1/2 text-gray-500">
                                $
                              </span>
                              <Input
                                type="text"
                                className="pl-8"
                                value={
                                  field.value
                                    ? Number(field.value).toLocaleString(
                                        'es-CO',
                                      )
                                    : ''
                                }
                                onChange={(e) => {
                                  const value = parseCurrencyInput(
                                    e.target.value,
                                  );
                                  field.onChange(value ? parseInt(value) : 0);
                                }}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="monthlyExpenses"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Gastos Mensuales</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <span className="absolute top-1/2 left-3 -translate-y-1/2 text-gray-500">
                                $
                              </span>
                              <Input
                                type="text"
                                className="pl-8"
                                value={
                                  field.value
                                    ? Number(field.value).toLocaleString(
                                        'es-CO',
                                      )
                                    : ''
                                }
                                onChange={(e) => {
                                  const value = parseCurrencyInput(
                                    e.target.value,
                                  );
                                  field.onChange(value ? parseInt(value) : 0);
                                }}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="bankName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Banco</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value ?? ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="accountType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tipo de Cuenta</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="AHORROS">Ahorros</SelectItem>
                              <SelectItem value="CORRIENTE">
                                Corriente
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="bankAccount"
                      render={({ field }) => (
                        <FormItem className="col-span-2">
                          <FormLabel>Número de Cuenta</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value ?? ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="contact" className="mt-4 space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center text-lg">
                      <MapPin className="mr-2 h-5 w-5" />
                      Información de Contacto
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input type="email" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Teléfono</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem className="col-span-2">
                          <FormLabel>Dirección</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Ciudad</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="department"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Departamento</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="emergencyContact"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Contacto de Emergencia</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value ?? ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="emergencyPhone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Teléfono de Emergencia</FormLabel>
                          <FormControl>
                            <Input {...field} value={field.value ?? ''} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem className="col-span-2">
                          <FormLabel>Notas Adicionales</FormLabel>
                          <FormControl>
                            <textarea
                              className="border-input bg-background w-full rounded-md border px-3 py-2 text-sm"
                              rows={3}
                              {...field}
                              value={field.value ?? ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            <div className="mt-6 flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending ? 'Guardando...' : 'Guardar Cambios'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
