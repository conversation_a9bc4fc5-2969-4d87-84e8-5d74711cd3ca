'use client';

import { Eye, Edit, Trash2 } from 'lucide-react';
import { useState, useTransition } from 'react';
import { toast } from 'sonner';

import { But<PERSON> } from '@/components/ui/button';
import { updateUser, deleteUser } from '@/features/user/actions';

import { DeleteUserDialog } from './components/delete-user-dialog';
import { EditUserDialog } from './components/edit-user-dialog';
import { UserDetailsDialog } from './components/user-details-dialog';

import type { UIUser, UIRole } from '@/features/user/types';

interface UserTableActionsProps {
  user: UIUser;
  roles: UIRole[];
  onUserUpdated?: () => void;
}

export function UserTableActions({
  user,
  roles,
  onUserUpdated,
}: Readonly<UserTableActionsProps>) {
  const [isPending, startTransition] = useTransition();
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const handleUserUpdated = () => {
    setShowEditDialog(false);
    onUserUpdated?.();
  };

  const handleDeleteUser = (userId: string) => {
    startTransition(async () => {
      const result = await deleteUser(userId);
      if (result.success) {
        toast.success(result.message);
        setShowDeleteDialog(false);
        onUserUpdated?.();
      } else {
        toast.error(result.message);
      }
    });
  };

  const handleToggleStatus = (userId: string) => {
    startTransition(async () => {
      const newStatus = user.status === 'Activo' ? 'Inactivo' : 'Activo';
      const result = await updateUser({
        id: userId,
        status: newStatus,
      });

      if (result.success) {
        toast.success(`Usuario ahora está ${newStatus.toLowerCase()}.`);
        onUserUpdated?.();
      } else {
        toast.error(result.message);
      }
    });
  };

  const resetUserPassword = () => {
    alert('Password reset functionality would be implemented here');
  };

  return (
    <>
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowDetailsDialog(true)}
          title="Ver detalles"
          disabled={isPending}
        >
          <Eye className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowEditDialog(true)}
          title="Editar usuario"
          disabled={isPending}
        >
          <Edit className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowDeleteDialog(true)}
          title="Eliminar usuario"
          disabled={user.role.name === 'Operadora de Insolvencia' || isPending}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>

      <UserDetailsDialog
        open={showDetailsDialog}
        onOpenChange={setShowDetailsDialog}
        user={user}
        onToggleStatus={handleToggleStatus}
        onResetPassword={resetUserPassword}
      />

      <EditUserDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        user={user}
        onUserUpdated={handleUserUpdated}
        roles={roles}
      />

      <DeleteUserDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        user={user}
        onDeleteUser={handleDeleteUser}
      />
    </>
  );
}
