import { z } from 'zod';

export const createUserSchema = z
  .object({
    name: z.string().min(1, 'El nombre es requerido'),
    email: z.string().email('Email inválido'),
    status: z.string().default('Activo'),
    professionalCard: z.string().min(1, 'La tarjeta profesional es requerida'),
    phone: z.string().min(1, 'El teléfono es requerido'),
    address: z.string().min(1, 'La dirección es requerida'),
    roleId: z.string().min(1, 'El rol es requerido'),
    password: z
      .string()
      .min(6, 'La contraseña debe tener al menos 6 caracteres')
      .optional(),
    confirmPassword: z.string().optional(),
    sendWelcomeEmail: z.boolean().optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Las contraseñas no coinciden',
    path: ['confirmPassword'],
  });

export const updateUserSchema = z.object({
  id: z.string().min(1, 'El ID es requerido'),
  name: z.string().min(1, 'El nombre es requerido').optional(),
  email: z.string().email('Email inválido').optional(),
  status: z.string().optional(),
  professionalCard: z
    .string()
    .min(1, 'La tarjeta profesional es requerida')
    .optional(),
  phone: z.string().min(1, 'El teléfono es requerido').optional(),
  address: z.string().min(1, 'La dirección es requerida').optional(),
  roleId: z.string().min(1, 'El rol es requerido').optional(),
  password: z
    .string()
    .min(6, 'La contraseña debe tener al menos 6 caracteres')
    .optional(),
});

export const editUserFormSchema = z.object({
  name: z.string().min(1, 'El nombre es obligatorio'),
  email: z.string().email('Email inválido'),
  phone: z.string(),
  address: z.string(),
  roleId: z.string().min(1, 'El rol es obligatorio'),
  professionalCard: z.string(),
  status: z.string(),
});

export const createUserFormSchema = z
  .object({
    name: z.string().min(1, 'El nombre es requerido'),
    email: z.string().email('Email inválido'),
    status: z.string(),
    professionalCard: z.string().min(1, 'La tarjeta profesional es requerida'),
    phone: z.string().min(1, 'El teléfono es requerido'),
    address: z.string().min(1, 'La dirección es requerida'),
    roleId: z.string().min(1, 'El rol es requerido'),
    password: z
      .string()
      .min(6, 'La contraseña debe tener al menos 6 caracteres')
      .optional(),
    confirmPassword: z.string().optional(),
    sendWelcomeEmail: z.boolean().optional(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Las contraseñas no coinciden',
    path: ['confirmPassword'],
  });

export type CreateUserData = z.infer<typeof createUserSchema>;
export type UpdateUserData = z.infer<typeof updateUserSchema>;
export type EditUserFormData = z.infer<typeof editUserFormSchema>;
export type CreateUserFormData = z.infer<typeof createUserFormSchema>;
