'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import {
  User as UserIcon,
  Mail,
  Phone,
  MapPin,
  Shield,
  CreditCard,
} from 'lucide-react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { createUserFormSchema } from '@/features/user/schemas';
import { getPermissionName } from '@/lib/permissions';

import type { CreateUserFormData } from '@/features/user/schemas';
import type { UIRole } from '@/features/user/types';

interface CreateUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateUser: (userData: CreateUserFormData) => void;
  roles: UIRole[];
}

export function CreateUserDialog({
  open,
  onOpenChange,
  onCreateUser,
  roles,
}: Readonly<CreateUserDialogProps>) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
    watch,
  } = useForm<CreateUserFormData>({
    resolver: zodResolver(createUserFormSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      address: '',
      roleId: '',
      professionalCard: '',
      status: 'Activo',
      sendWelcomeEmail: true,
      password: '',
      confirmPassword: '',
    },
  });

  const watchedRoleId = watch('roleId');
  const selectedRole = roles.find((r) => r.id === watchedRoleId);

  const onSubmit = (data: CreateUserFormData) => {
    onCreateUser(data);
    reset();
  };

  const generatePassword = () => {
    const password = window.crypto
      .randomUUID()
      .replaceAll('-', '')
      .slice(0, 16);
    setValue('password', password);
    setValue('confirmPassword', password);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle>Crear Nuevo Usuario</DialogTitle>
          <DialogDescription>
            Complete la información para crear un nuevo usuario en el sistema
          </DialogDescription>
        </DialogHeader>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            void handleSubmit(onSubmit)();
          }}
          className="space-y-4"
        >
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">Nombre Completo *</Label>
              <div className="relative">
                <UserIcon className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                <Input
                  id="name"
                  placeholder="Nombre completo"
                  {...register('name')}
                  className={`pl-10 ${errors.name ? 'border-red-500' : ''}`}
                />
              </div>
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Correo Electrónico *</Label>
              <div className="relative">
                <Mail className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register('email')}
                  className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
                />
              </div>
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="phone">Teléfono</Label>
              <div className="relative">
                <Phone className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                <Input
                  id="phone"
                  placeholder="+57 ************"
                  {...register('phone')}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="professionalCard">Tarjeta Profesional</Label>
              <div className="relative">
                <CreditCard className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                <Input
                  id="professionalCard"
                  placeholder="123.456"
                  {...register('professionalCard')}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="address">Dirección</Label>
            <div className="relative">
              <MapPin className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
              <Input
                id="address"
                placeholder="Dirección completa"
                {...register('address')}
                className="pl-10"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="role">Rol *</Label>
            <div className="relative">
              <Shield className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
              <Select
                value={watch('roleId')}
                onValueChange={(value) => setValue('roleId', value)}
              >
                <SelectTrigger
                  className={`pl-10 ${errors.roleId ? 'border-red-500' : ''}`}
                >
                  <SelectValue placeholder="Seleccionar rol" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role?.id ?? ''} value={role?.id ?? ''}>
                      {role?.name ?? 'N/A'}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {errors.roleId && (
              <p className="text-sm text-red-500">{errors.roleId.message}</p>
            )}
          </div>

          <div className="space-y-4 rounded-lg border bg-blue-50 p-4">
            <h3 className="font-medium">Configuración de Acceso</h3>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="password">Contraseña *</Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Mínimo 6 caracteres"
                  {...register('password')}
                  className={errors.password ? 'border-red-500' : ''}
                />
                {errors.password && (
                  <p className="text-sm text-red-500">
                    {errors.password.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirmar Contraseña *</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  placeholder="Repetir contraseña"
                  {...register('confirmPassword')}
                  className={errors.confirmPassword ? 'border-red-500' : ''}
                />
                {errors.confirmPassword && (
                  <p className="text-sm text-red-500">
                    {errors.confirmPassword.message}
                  </p>
                )}
              </div>
            </div>

            <Button
              type="button"
              onClick={generatePassword}
              variant="outline"
              size="sm"
            >
              Generar Contraseña Automática
            </Button>

            <div className="flex items-center space-x-2">
              <Switch
                id="sendWelcomeEmail"
                checked={watch('sendWelcomeEmail')}
                onCheckedChange={(checked) =>
                  setValue('sendWelcomeEmail', checked)
                }
              />
              <Label htmlFor="sendWelcomeEmail">
                Enviar email de bienvenida con credenciales
              </Label>
            </div>
          </div>

          {selectedRole && (
            <div className="rounded-lg border bg-green-50 p-4">
              <h3 className="mb-2 font-medium">
                Permisos del Rol: {selectedRole?.name ?? 'N/A'}
              </h3>
              <ul className="space-y-1 text-sm text-gray-600">
                {(selectedRole?.permissions ?? []).map((permission: string) => (
                  <li key={permission} className="flex items-center">
                    <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
                    {getPermissionName(permission)}
                  </li>
                ))}
              </ul>
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Creando...' : 'Crear Usuario'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
