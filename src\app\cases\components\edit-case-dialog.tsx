'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { updateCase } from '@/features/case/actions';
import {
  type Asset,
  type Debt,
  type LegalProcess,
  type CaseData,
} from '@/features/case/types';
import { getCreditors } from '@/features/creditor/actions';
import { getDebtors, createDebtor } from '@/features/debtor/actions';

import { AssetsSection } from './create-case-dialog/assets-section';
import { CreateDebtorDialog } from './create-case-dialog/create-debtor-dialog';
import { CreditorsSection } from './create-case-dialog/creditors-section';
import { DebtorSection } from './create-case-dialog/debtor-section';
import { DocumentsSection } from './create-case-dialog/documents-section';

interface EditCaseDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  case: CaseData;
  onCaseUpdated: () => void;
}

interface DebtorOption {
  id: string;
  name: string;
  idNumber: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  department: string;
  monthlyIncome: number;
  monthlyExpenses: number;
}

interface CreditorOption {
  id: string;
  name: string;
  nit: string;
  type: string;
}

interface NewDebtForm {
  creditor: string;
  creditorId: string | undefined;
  amount: string;
  type: string;
  interestRate: string;
}

interface FormData {
  debtorName: string;
  debtorId: string;
  debtorEmail: string;
  debtorPhone: string;
  debtorAddress: string;
  caseType: string;
  totalDebt: string;
  monthlyIncome: string;
  monthlyExpenses: string;
  operatorId: string;
  causes: string[];
  debts: Debt[];
  assets: Asset[];
  legalProcesses: LegalProcess[];
}

export function EditCaseDialog({
  open,
  onOpenChange,
  case: caseData,
  onCaseUpdated,
}: Readonly<EditCaseDialogProps>) {
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(false);
  const [debtors, setDebtors] = useState<DebtorOption[]>([]);
  const [creditors, setCreditors] = useState<CreditorOption[]>([]);
  const [selectedDebtorId, setSelectedDebtorId] = useState<string>('');
  const [selectedDebtor, setSelectedDebtor] = useState<DebtorOption | null>(
    null,
  );
  const [showCreateDebtorDialog, setShowCreateDebtorDialog] = useState(false);
  const [newDebtorForm, setNewDebtorForm] = useState({
    name: '',
    idNumber: '',
    email: '',
    phone: '',
    address: '',
    city: 'Bogotá',
    department: 'Cundinamarca',
    monthlyIncome: 0,
    monthlyExpenses: 0,
  });
  const [creatingDebtor, setCreatingDebtor] = useState(false);

  const [formData, setFormData] = useState<FormData>({
    debtorName: '',
    debtorId: '',
    debtorEmail: '',
    debtorPhone: '',
    debtorAddress: '',
    caseType: '',
    totalDebt: '',
    monthlyIncome: '',
    monthlyExpenses: '',
    operatorId: '',
    causes: [],
    debts: [],
    assets: [],
    legalProcesses: [],
  });

  useEffect(() => {
    if (open && caseData) {
      const loadData = async () => {
        setDataLoading(true);
        try {
          const [debtorsData, creditorsData] = await Promise.all([
            getDebtors(),
            getCreditors(),
          ]);
          setDebtors(debtorsData as DebtorOption[]);
          setCreditors(creditorsData as CreditorOption[]);

          setSelectedDebtorId(caseData.debtorId);

          let caseTypeValue = '';
          if (caseData.type === 'Insolvencia') caseTypeValue = 'INSOLVENCY';
          else if (caseData.type === 'Conciliación')
            caseTypeValue = 'CONCILIATION';
          else if (caseData.type === 'Acuerdo de Apoyo')
            caseTypeValue = 'SUPPORT_AGREEMENT';

          setFormData({
            debtorName: caseData.debtorName,
            debtorId: caseData.debtor.idNumber,
            debtorEmail: caseData.debtor.email,
            debtorPhone: caseData.debtor.phone,
            debtorAddress: '',
            caseType: caseTypeValue,
            totalDebt: caseData.totalDebt.toString(),
            monthlyIncome: '0',
            monthlyExpenses: '0',
            operatorId: caseData.operatorId,
            causes: caseData.causes || [],
            debts: (caseData.debts ?? []).map((debt) => ({
              id: debt.id,
              creditor: debt.creditor.name,
              creditorId: debt.creditor.id,
              amount: debt.amount,
              type: debt.type,
              interestRate: debt.interestRate,
            })),
            assets: caseData.assets ?? [],
            legalProcesses: [],
          });
        } catch {
          toast.error('Error al cargar los datos');
        } finally {
          setDataLoading(false);
        }
      };
      void loadData();
    }
  }, [open, caseData]);

  useEffect(() => {
    if (!showCreateDebtorDialog && open) {
      const reloadDebtors = async () => {
        try {
          const debtorsData = await getDebtors();
          setDebtors(debtorsData as DebtorOption[]);
        } catch {}
      };
      void reloadDebtors();
    }
  }, [showCreateDebtorDialog, open]);

  useEffect(() => {
    if (selectedDebtorId && debtors.length > 0) {
      const debtor = debtors.find(
        (d: DebtorOption) => d.id === selectedDebtorId,
      );
      if (debtor) {
        setSelectedDebtor(debtor);
        setFormData((prev: FormData) => ({
          ...prev,
          debtorName: debtor.name,
          debtorId: debtor.idNumber,
          debtorEmail: debtor.email,
          debtorPhone: debtor.phone,
          debtorAddress: `${debtor.address}, ${debtor.city}, ${debtor.department}`,
          monthlyIncome: debtor.monthlyIncome.toString(),
          monthlyExpenses: debtor.monthlyExpenses.toString(),
        }));
      }
    }
  }, [selectedDebtorId, debtors]);

  const [newDebt, setNewDebt] = useState<NewDebtForm>({
    creditor: '',
    creditorId: undefined,
    amount: '',
    type: '',
    interestRate: '',
  });

  const insolvencyCauses = [
    'Sobreendeudamiento por falta de recursos',
    'Pérdida de empleo',
    'Divorcio',
    'Enfermedad',
    'Accidente',
    'Estafa',
    'Disminución de ingresos',
    'Falta de educación financiera',
    'Muerte de cónyuge',
    'Nuevos créditos para cubrir otros',
    'Mala inversión',
  ];

  const handleCauseChange = (cause: string) => {
    setFormData((prev: FormData) => {
      const newCauses = prev.causes.includes(cause)
        ? prev.causes.filter((c: string) => c !== cause)
        : [...prev.causes, cause];
      return { ...prev, causes: newCauses };
    });
  };

  const addDebt = () => {
    if (newDebt.creditor && newDebt.amount) {
      setFormData((prev: FormData) => ({
        ...prev,
        debts: [
          ...prev.debts,
          {
            ...newDebt,
            id: Date.now().toString(),
            amount: Number(newDebt.amount),
            interestRate: Number(newDebt.interestRate),
          },
        ],
      }));
      setNewDebt({
        creditor: '',
        creditorId: undefined,
        amount: '',
        type: '',
        interestRate: '',
      });
    }
  };

  const removeDebt = (debtId: string) => {
    setFormData((prev: FormData) => ({
      ...prev,
      debts: prev.debts.filter((debt: Debt) => debt.id !== debtId),
    }));
  };

  const handleSubmit = async () => {
    if (!selectedDebtorId) {
      toast.error('Por favor seleccione un deudor');
      return;
    }

    if (!formData.caseType) {
      toast.error('Por favor seleccione el tipo de caso');
      return;
    }

    if (!formData.totalDebt || Number(formData.totalDebt) <= 0) {
      toast.error('Por favor ingrese el monto total de la deuda');
      return;
    }

    setLoading(true);
    try {
      const { success, message } = await updateCase({
        id: caseData.id,
        debtorId: selectedDebtorId,
        type: formData.caseType as
          | 'INSOLVENCY'
          | 'CONCILIATION'
          | 'SUPPORT_AGREEMENT',
        status: caseData.status as
          | 'NEGOTIATION'
          | 'HEARING_SCHEDULED'
          | 'PENDING_DOCUMENTS'
          | 'AGREEMENT_APPROVED'
          | 'CLOSED',
        totalDebt: Number(formData.totalDebt),
        creditors: formData.debts.length || 1,
        operatorId: formData.operatorId,
        phase: caseData.phase ?? undefined,
        causes: formData.causes,
      });

      if (success) {
        toast.success(message);
        onCaseUpdated();
        onOpenChange(false);
      } else {
        toast.error(message);
      }
    } catch {
      toast.error('Error al actualizar el caso');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateDebtor = async () => {
    if (
      !newDebtorForm.name ||
      !newDebtorForm.idNumber ||
      !newDebtorForm.email ||
      !newDebtorForm.phone
    ) {
      toast.error('Por favor complete todos los campos requeridos');
      return;
    }

    setCreatingDebtor(true);
    try {
      const { success, message, data } = await createDebtor({
        ...newDebtorForm,
        idType: 'CC',
        occupation: 'Empleado',
        dependents: 0,
        maritalStatus: 'SOLTERO',
        educationLevel: 'UNIVERSITARIO',
        emergencyContact: '',
        emergencyPhone: '',
        bankAccount: '',
        bankName: '',
        accountType: 'AHORROS',
        description: '',
      });

      if (success) {
        toast.success('Deudor creado exitosamente');
        setShowCreateDebtorDialog(false);
        setNewDebtorForm({
          name: '',
          idNumber: '',
          email: '',
          phone: '',
          address: '',
          city: 'Bogotá',
          department: 'Cundinamarca',
          monthlyIncome: 0,
          monthlyExpenses: 0,
        });
        const debtorsData = await getDebtors();
        setDebtors(debtorsData as DebtorOption[]);
        if (data?.id) {
          setSelectedDebtorId(data.id);
        }
      } else {
        toast.error(message);
      }
    } catch {
      toast.error('Error al crear el deudor');
    } finally {
      setCreatingDebtor(false);
    }
  };

  useEffect(() => {
    if (!open) {
      setSelectedDebtorId('');
      setSelectedDebtor(null);
      setFormData({
        debtorName: '',
        debtorId: '',
        debtorEmail: '',
        debtorPhone: '',
        debtorAddress: '',
        caseType: '',
        totalDebt: '',
        monthlyIncome: '',
        monthlyExpenses: '',
        operatorId: '',
        causes: [],
        debts: [],
        assets: [],
        legalProcesses: [],
      });
      setNewDebtorForm({
        name: '',
        idNumber: '',
        email: '',
        phone: '',
        address: '',
        city: 'Bogotá',
        department: 'Cundinamarca',
        monthlyIncome: 0,
        monthlyExpenses: 0,
      });
    }
  }, [open]);

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-6xl">
          <DialogHeader>
            <DialogTitle>Editar Caso {caseData.caseNumber}</DialogTitle>
            <DialogDescription>
              Modifique la información del caso de {caseData.debtorName}. Los
              cambios se aplicarán inmediatamente.
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="debtor" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="debtor">Deudor</TabsTrigger>
              <TabsTrigger value="creditors">Acreedores</TabsTrigger>
              <TabsTrigger value="assets">Bienes</TabsTrigger>
              <TabsTrigger value="documents">Documentos</TabsTrigger>
            </TabsList>

            <TabsContent value="debtor" className="space-y-4">
              <DebtorSection
                debtors={debtors}
                selectedDebtorId={selectedDebtorId}
                selectedDebtor={selectedDebtor}
                formData={{
                  caseType: formData.caseType,
                  totalDebt: formData.totalDebt,
                  causes: formData.causes,
                }}
                insolvencyCauses={insolvencyCauses}
                onDebtorSelect={setSelectedDebtorId}
                onCreateDebtorClick={() => setShowCreateDebtorDialog(true)}
                onFormDataChange={(data: Partial<FormData>) =>
                  setFormData((prev: FormData) => ({ ...prev, ...data }))
                }
                onCauseChange={handleCauseChange}
                loading={dataLoading}
              />
            </TabsContent>

            <TabsContent value="creditors" className="space-y-4">
              <CreditorsSection
                creditors={creditors}
                newDebt={newDebt}
                debts={formData.debts}
                onNewDebtChange={(debt) =>
                  setNewDebt({
                    creditor: debt.creditor,
                    creditorId: debt.creditorId ?? undefined,
                    amount: debt.amount,
                    type: debt.type,
                    interestRate: debt.interestRate,
                  })
                }
                onAddDebt={addDebt}
                onRemoveDebt={removeDebt}
              />
            </TabsContent>

            <TabsContent value="assets" className="space-y-4">
              <AssetsSection />
            </TabsContent>

            <TabsContent value="documents" className="space-y-4">
              <DocumentsSection />
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancelar
            </Button>
            <Button onClick={() => void handleSubmit()} disabled={loading}>
              {loading ? 'Guardando Cambios...' : 'Guardar Cambios'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <CreateDebtorDialog
        open={showCreateDebtorDialog}
        onOpenChange={setShowCreateDebtorDialog}
        newDebtorForm={newDebtorForm}
        creatingDebtor={creatingDebtor}
        onFormChange={setNewDebtorForm}
        onSubmit={handleCreateDebtor}
      />
    </>
  );
}
