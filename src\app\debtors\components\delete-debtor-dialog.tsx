'use client';

import { <PERSON><PERSON><PERSON>riangle, Loader2 } from 'lucide-react';
import { useRef, useTransition } from 'react';
import { toast } from 'sonner';

import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { deleteDebtor } from '@/features/debtor/actions';

import type { UIDebtorWithRelations } from '@/features/debtor/types';

interface DeleteDebtorDialogProps {
  debtor: UIDebtorWithRelations | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onDebtorDeleted?: () => void;
}

export function DeleteDebtorDialog({
  debtor,
  open,
  onOpenChange,
  onDebtorDeleted,
}: Readonly<DeleteDebtorDialogProps>) {
  const [isPending, startTransition] = useTransition();
  const closeRef = useRef<HTMLButtonElement>(null);

  const handleDelete = () => {
    if (!debtor) return;

    startTransition(async () => {
      const result = await deleteDebtor(debtor.id);
      if (result.success) {
        toast.success('Deudor eliminado exitosamente', {
          description: `El deudor ${debtor.name} ha sido eliminado correctamente`,
        });
        closeRef.current?.click();
        onDebtorDeleted?.();
      } else {
        toast.error(result.message || 'Error al eliminar el deudor');
      }
    });
  };

  if (!debtor) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Eliminar Deudor</DialogTitle>
          <DialogDescription>
            ¿Está seguro de que desea eliminar este deudor?
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Esta acción no se puede deshacer. Se eliminarán permanentemente
              los datos del deudor
              <strong className="mt-2 block">{debtor.name}</strong>
            </AlertDescription>
          </Alert>

          {debtor.activeCases > 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Este deudor tiene{' '}
                <strong>{debtor.activeCases} casos activos</strong>. Asegúrese
                de reasignar o cerrar estos casos antes de eliminar el deudor.
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <DialogClose asChild>
            <Button ref={closeRef} variant="outline" disabled={isPending}>
              Cancelar
            </Button>
          </DialogClose>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isPending || debtor._count.cases > 0}
          >
            {isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Eliminando...
              </>
            ) : (
              'Eliminar'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
