'use client';

import { AlertTriangle } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { deleteCase } from '@/features/case/actions';

import type { CaseData } from '@/features/case/types';

interface DeleteCaseDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  case: CaseData;
  onCaseDeleted: () => void;
}

export function DeleteCaseDialog({
  open,
  onOpenChange,
  case: caseData,
  onCaseDeleted,
}: Readonly<DeleteCaseDialogProps>) {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const result = await deleteCase(caseData?.id ?? '');

      if (result.success) {
        toast.success(result.message);
        onCaseDeleted();
        onOpenChange(false);
      } else {
        toast.error(result.message);
      }
    } catch {
      toast.error('Error al eliminar el caso');
    } finally {
      setIsDeleting(false);
    }
  };

  const hasDocuments =
    (caseData?.documents?.length ?? caseData?._count?.documents ?? 0) > 0;
  const hasAssets = (caseData?.assets?.length ?? 0) > 0;
  const hasDebts = (caseData?.debts?.length ?? 0) > 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <DialogTitle>Eliminar Caso</DialogTitle>
          </div>
          <DialogDescription>
            ¿Está seguro de que desea eliminar el caso{' '}
            {caseData?.caseNumber ?? 'N/A'}?
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="rounded-lg bg-red-50 p-4">
            <h4 className="font-medium text-red-900">Información del Caso</h4>
            <div className="mt-2 space-y-1 text-sm text-red-700">
              <p>
                <strong>Caso:</strong> {caseData.caseNumber}
              </p>
              <p>
                <strong>Deudor:</strong> {caseData.debtorName}
              </p>
              <p>
                <strong>Tipo:</strong> {caseData.type}
              </p>
              <p>
                <strong>Estado:</strong> {caseData.status}
              </p>
              <p>
                <strong>Deuda Total:</strong> $
                {caseData.totalDebt.toLocaleString()}
              </p>
            </div>
          </div>

          {(hasDocuments || hasAssets || hasDebts) && (
            <div className="rounded-lg bg-yellow-50 p-4">
              <h4 className="font-medium text-yellow-900">Advertencia</h4>
              <p className="mt-1 text-sm text-yellow-700">
                Este caso tiene información asociada que también será eliminada:
              </p>
              <ul className="mt-2 space-y-1 text-sm text-yellow-700">
                {hasDocuments && (
                  <li>
                    • {caseData.documents?.length ?? caseData._count.documents}{' '}
                    documento(s)
                  </li>
                )}
                {hasAssets && (
                  <li>• {caseData.assets?.length ?? 0} activo(s)</li>
                )}
                {hasDebts && <li>• {caseData.debts?.length ?? 0} deuda(s)</li>}
              </ul>
            </div>
          )}

          <div className="rounded-lg bg-gray-50 p-4">
            <p className="text-sm text-gray-600">
              <strong>Nota:</strong> Esta acción no se puede deshacer. Una vez
              eliminado, toda la información del caso se perderá
              permanentemente.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button
            variant="destructive"
            onClick={() => void handleDelete()}
            disabled={isDeleting}
          >
            {isDeleting ? 'Eliminando...' : 'Eliminar Caso'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
