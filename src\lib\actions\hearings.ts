import { revalidatePath } from 'next/cache';
import { z } from 'zod';

import prisma from '@/lib/prisma';

import type { ActionResult } from '@/lib/types';
import type { Case, Prisma } from '@prisma/client';

// Schemas
export const scheduleHearingSchema = z.object({
  caseId: z.string().min(1, 'El ID del caso es requerido'),
  hearingDate: z.string().min(1, 'La fecha de audiencia es requerida'),
  notes: z.string().optional(),
});

export const rescheduleHearingSchema = z.object({
  caseId: z.string().min(1, 'El ID del caso es requerido'),
  newHearingDate: z.string().min(1, 'La nueva fecha de audiencia es requerida'),
  reason: z.string().min(1, 'La razón es requerida'),
});

export const cancelHearingSchema = z.object({
  caseId: z.string().min(1, 'El ID del caso es requerido'),
  reason: z.string().min(1, 'La razón es requerida'),
});

// Types
export type CreateHearingData = z.infer<typeof scheduleHearingSchema>;
export type RescheduleHearingData = z.infer<typeof rescheduleHearingSchema>;
export type CancelHearingData = z.infer<typeof cancelHearingSchema>;

export interface HearingFilter {
  startDate?: string;
  endDate?: string;
  operatorId?: string;
  caseType?: string;
  status?: string;
}

type CaseWithHearing = Prisma.CaseGetPayload<{
  include: {
    debtor: {
      select: {
        id: true;
        name: true;
        email: true;
        phone: true;
      };
    };
    operator: {
      select: {
        id: true;
        name: true;
        email: true;
      };
    };
  };
}>;

const mapCaseTypeToSpanish = (type: string): string => {
  const typeMap: Record<string, string> = {
    INSOLVENCY: 'Insolvencia',
    CONCILIATION: 'Conciliación',
    SUPPORT_AGREEMENT: 'Acuerdo de Apoyo',
  };
  return typeMap[type] || type;
};

const mapCaseStatusToSpanish = (status: string): string => {
  const statusMap: Record<string, string> = {
    NEGOTIATION: 'En negociación',
    HEARING_SCHEDULED: 'Audiencia programada',
    PENDING_DOCUMENTS: 'Documentos pendientes',
    AGREEMENT_APPROVED: 'Acuerdo aprobado',
    CLOSED: 'Cerrado',
  };
  return statusMap[status] || status;
};

export async function getUpcomingHearings(filters?: HearingFilter) {
  try {
    const where: Prisma.CaseWhereInput = {
      hearingDate: {
        not: null,
        gte: new Date(),
      },
    };

    if (filters?.startDate && filters?.endDate) {
      where.hearingDate = {
        gte: new Date(filters.startDate),
        lte: new Date(filters.endDate),
      };
    }

    if (filters?.operatorId) {
      where.operatorId = filters.operatorId;
    }

    if (filters?.caseType) {
      where.type = filters.caseType;
    }

    if (filters?.status) {
      where.status = filters.status;
    }

    const hearings = await prisma.case.findMany({
      where,
      include: {
        debtor: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        hearingDate: 'asc',
      },
    });

    return hearings.map((hearing: CaseWithHearing) => ({
      ...hearing,
      type: mapCaseTypeToSpanish(hearing.type),
      status: mapCaseStatusToSpanish(hearing.status),
    }));
  } catch (error) {
    console.error('Error fetching upcoming hearings:', error);
    throw new Error('Error al obtener las audiencias próximas');
  }
}

export async function getAllHearings(filters?: HearingFilter) {
  try {
    const where: Prisma.CaseWhereInput = {
      hearingDate: {
        not: null,
      },
    };

    if (filters?.startDate && filters?.endDate) {
      where.hearingDate = {
        gte: new Date(filters.startDate),
        lte: new Date(filters.endDate),
      };
    }

    if (filters?.operatorId) {
      where.operatorId = filters.operatorId;
    }

    if (filters?.caseType) {
      where.type = filters.caseType;
    }

    if (filters?.status) {
      where.status = filters.status;
    }

    const hearings = await prisma.case.findMany({
      where,
      include: {
        debtor: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        hearingDate: 'asc',
      },
    });

    return hearings.map((hearing: CaseWithHearing) => ({
      ...hearing,
      type: mapCaseTypeToSpanish(hearing.type),
      status: mapCaseStatusToSpanish(hearing.status),
    }));
  } catch (error) {
    console.error('Error fetching hearings:', error);
    throw new Error('Error al obtener las audiencias');
  }
}

export async function scheduleHearing(
  values: CreateHearingData,
): Promise<ActionResult<Case>> {
  'use server';

  const { success, data, error } = scheduleHearingSchema.safeParse(values);

  if (!success) {
    return {
      success: false,
      message: 'Por favor complete todos los campos requeridos',
      errors: error.flatten().fieldErrors,
    };
  }

  try {
    const hearingDate = new Date(data.hearingDate);

    if (hearingDate <= new Date()) {
      throw new Error('La fecha de la audiencia debe ser futura');
    }

    const hearing = await prisma.case.update({
      where: { id: data.caseId },
      data: {
        hearingDate,
        status: 'HEARING_SCHEDULED',
      },
      include: {
        debtor: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    revalidatePath('/hearings');

    return {
      success: true,
      message: 'Audiencia programada exitosamente',
      data: hearing,
    };
  } catch (error) {
    console.error('Error scheduling hearing:', error);

    if (error instanceof Error) {
      return {
        success: false,
        message: error.message,
      };
    }

    return {
      success: false,
      message: 'Error al programar la audiencia',
    };
  }
}

export async function rescheduleHearing(
  values: RescheduleHearingData,
): Promise<ActionResult<Case>> {
  'use server';

  const { success, data, error } = rescheduleHearingSchema.safeParse(values);

  if (!success) {
    return {
      success: false,
      message: 'Por favor complete todos los campos requeridos',
      errors: error.flatten().fieldErrors,
    };
  }

  try {
    const newHearingDate = new Date(data.newHearingDate);

    if (newHearingDate <= new Date()) {
      throw new Error('La nueva fecha de la audiencia debe ser futura');
    }

    const hearing = await prisma.case.update({
      where: { id: data.caseId },
      data: {
        hearingDate: newHearingDate,
      },
      include: {
        debtor: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    revalidatePath('/hearings');

    return {
      success: true,
      message: 'Audiencia reprogramada exitosamente',
      data: hearing,
    };
  } catch (error) {
    console.error('Error rescheduling hearing:', error);
    return {
      success: false,
      message: 'Error al reprogramar la audiencia',
    };
  }
}

export async function cancelHearing(
  values: CancelHearingData,
): Promise<ActionResult<Case>> {
  'use server';

  const { success, data, error } = cancelHearingSchema.safeParse(values);

  if (!success) {
    return {
      success: false,
      message: 'Por favor complete todos los campos requeridos',
      errors: error.flatten().fieldErrors,
    };
  }

  const { caseId } = data;
  if (!caseId) {
    return {
      success: false,
      message: 'ID del caso es requerido',
    };
  }

  try {
    const updatedCase = await prisma.case.update({
      where: { id: caseId },
      data: {
        hearingDate: null,
        status: 'NEGOTIATION',
      },
    });

    revalidatePath('/hearings');

    return {
      success: true,
      message: 'Audiencia cancelada exitosamente',
      data: updatedCase,
    };
  } catch (error) {
    console.error('Error canceling hearing:', error);
    return {
      success: false,
      message: 'Error al cancelar la audiencia',
    };
  }
}

export async function getHearingStats() {
  try {
    const today = new Date();
    const startOfWeek = new Date(
      today.setDate(today.getDate() - today.getDay()),
    );
    const endOfWeek = new Date(
      today.setDate(today.getDate() - today.getDay() + 6),
    );
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    const [total, thisWeek, thisMonth, upcoming] = await Promise.all([
      prisma.case.count({
        where: {
          hearingDate: { not: null },
        },
      }),
      prisma.case.count({
        where: {
          hearingDate: {
            gte: startOfWeek,
            lte: endOfWeek,
          },
        },
      }),
      prisma.case.count({
        where: {
          hearingDate: {
            gte: startOfMonth,
            lte: endOfMonth,
          },
        },
      }),
      prisma.case.count({
        where: {
          hearingDate: {
            gte: new Date(),
          },
        },
      }),
    ]);

    return {
      total,
      thisWeek,
      thisMonth,
      upcoming,
    };
  } catch (error) {
    console.error('Error fetching hearing stats:', error);
    throw new Error('Error al obtener las estadísticas de audiencias');
  }
}

export async function getHearingsByDateRange(
  startDate: string,
  endDate: string,
) {
  try {
    if (!startDate || !endDate) {
      throw new Error('Las fechas de inicio y fin son requeridas');
    }

    const filters: HearingFilter = {
      startDate,
      endDate,
    };

    return await getAllHearings(filters);
  } catch (error) {
    console.error('Error fetching hearings by date range:', error);
    throw new Error('Error al obtener las audiencias por rango de fechas');
  }
}

export async function getOperatorHearings(operatorId: string) {
  try {
    if (!operatorId) {
      throw new Error('ID del operador es requerido');
    }

    const filters: HearingFilter = {
      operatorId,
    };

    return await getUpcomingHearings(filters);
  } catch (error) {
    console.error('Error fetching operator hearings:', error);
    throw new Error('Error al obtener las audiencias del operador');
  }
}
