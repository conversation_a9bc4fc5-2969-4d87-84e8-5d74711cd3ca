import { z } from 'zod';

export const createDebtorSchema = z.object({
  name: z.string().min(1, 'El nombre es requerido'),
  idNumber: z.string().min(1, 'El número de identificación es requerido'),
  idType: z.string().min(1, 'El tipo de identificación es requerido'),
  email: z.string().email('Email inválido'),
  phone: z.string().min(1, 'El teléfono es requerido'),
  address: z.string().min(1, 'La dirección es requerida'),
  city: z.string().min(1, 'La ciudad es requerida'),
  department: z.string().min(1, 'El departamento es requerido'),
  birthDate: z.date().optional(),
  maritalStatus: z.string().optional(),
  occupation: z.string().min(1, 'La ocupación es requerida'),
  monthlyIncome: z
    .number()
    .min(0, 'Los ingresos mensuales deben ser positivos'),
  monthlyExpenses: z
    .number()
    .min(0, 'Los gastos mensuales deben ser positivos')
    .optional(),
  dependents: z
    .number()
    .min(0, 'El número de dependientes debe ser positivo')
    .optional(),
  educationLevel: z.string().optional(),
  emergencyContact: z.string().optional(),
  emergencyPhone: z.string().optional(),
  bankAccount: z.string().optional(),
  bankName: z.string().optional(),
  accountType: z.string().optional(),
  description: z.string().optional(),
});

export const updateDebtorSchema = z.object({
  id: z.string().min(1, 'El ID es requerido'),
  name: z.string().min(1, 'El nombre es requerido').optional(),
  idNumber: z
    .string()
    .min(1, 'El número de identificación es requerido')
    .optional(),
  idType: z.string().optional(),
  email: z.string().email('Email inválido').optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  department: z.string().optional(),
  birthDate: z.date().optional(),
  maritalStatus: z.string().optional(),
  occupation: z.string().optional(),
  monthlyIncome: z
    .number()
    .min(0, 'Los ingresos mensuales deben ser positivos')
    .optional(),
  monthlyExpenses: z
    .number()
    .min(0, 'Los gastos mensuales deben ser positivos')
    .optional(),
  dependents: z
    .number()
    .min(0, 'El número de dependientes debe ser positivo')
    .optional(),
  educationLevel: z.string().optional(),
  emergencyContact: z.string().optional(),
  emergencyPhone: z.string().optional(),
  bankAccount: z.string().optional(),
  bankName: z.string().optional(),
  accountType: z.string().optional(),
  description: z.string().optional(),
});
