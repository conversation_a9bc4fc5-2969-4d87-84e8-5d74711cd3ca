import type { Prisma } from '@prisma/client';

type CreditorWithRelationsRaw = Prisma.CreditorGetPayload<{
  include: {
    contacts: true;
    debts: true;
    _count: {
      select: {
        debts: true;
      };
    };
  };
}>;

export type CreditorWithRelations = Omit<CreditorWithRelationsRaw, 'debts'> & {
  debts: (Omit<CreditorWithRelationsRaw['debts'][0], 'amount'> & {
    amount: number;
  })[];
};

export interface Creditor {
  id: string;
  name: string;
  type: string;
  email: string;
  phone: string;
  address: string;
  city: string | null;
  department: string | null;
  representative: string;
  nit: string;
  website: string | null;
  status: string;
  activeCases: number;
  createdDate: Date | null;
  lastUpdate: Date | null;
  description: string | null;
  representativeId: string | null;
  representativeEmail: string | null;
  representativePhone: string | null;
  bankName: string | null;
}

export interface Contact {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: string;
  creditorId: string;
}

export interface RecentCase {
  id: string;
  caseNumber: string;
  debtorName: string;
  status: string;
  totalDebt: number;
  createdDate: Date;
}

export interface ActivityLog {
  id: string;
  action: string;
  description: string;
  timestamp: Date;
  userId: string;
  userName: string;
}
