'use server';

import { revalidatePath } from 'next/cache';

import prisma from '@/lib/prisma';

import {
  createCreditorSchema,
  updateCreditorSchema,
  createContactSchema,
} from './schemas';

import type {
  CreateCreditorData,
  UpdateCreditorData,
  CreateContactData,
} from './types';
import type { ActionResult } from '@/lib/types';
import type {
  Creditor as PrismaCreditor,
  Contact as PrismaContact,
  Prisma,
} from '@prisma/client';

const convertDecimalToNumber = (
  decimal: Prisma.Decimal | null | undefined,
): number => {
  if (!decimal) return 0;
  return parseFloat(decimal.toString());
};

export async function getCreditors() {
  try {
    const creditors = await prisma.creditor.findMany({
      include: {
        contacts: true,
        debts: true,
        _count: {
          select: {
            debts: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    return creditors.map((creditor) => ({
      id: creditor.id,
      name: creditor.name,
      type: creditor.type,
      email: creditor.email,
      phone: creditor.phone,
      address: creditor.address,
      status: creditor.status,
      representative: creditor.representative,
      nit: creditor.nit,
      website: creditor.website,
      city: creditor.city,
      department: creditor.department,
      bankName: creditor.bankName,
      activeCases: creditor.activeCases,
      createdDate: creditor.createdDate,
      lastUpdate: creditor.lastUpdate,
      description: creditor.description,
      representativeId: creditor.representativeId,
      representativeEmail: creditor.representativeEmail,
      representativePhone: creditor.representativePhone,
      contacts: creditor.contacts,
      debts: creditor.debts.map((debt) => ({
        id: debt.id,
        amount: convertDecimalToNumber(debt.amount),
        interestRate: debt.interestRate,
        type: debt.type,
        caseId: debt.caseId,
        creditorId: debt.creditorId,
        debtorId: debt.debtorId,
      })),
      _count: creditor._count,
    }));
  } catch (error) {
    console.error('Error fetching creditors:', error);
    throw new Error('Error al obtener los acreedores');
  }
}

export async function getCreditorById(id: string) {
  try {
    if (!id) {
      throw new Error('ID del acreedor es requerido');
    }

    const creditor = await prisma.creditor.findUnique({
      where: { id },
      include: {
        contacts: true,
        debts: {
          include: {
            case: {
              include: {
                debtor: true,
              },
            },
          },
        },
        _count: {
          select: {
            debts: true,
          },
        },
      },
    });

    if (!creditor) {
      return null;
    }

    return {
      id: creditor.id,
      name: creditor.name,
      type: creditor.type,
      email: creditor.email,
      phone: creditor.phone,
      address: creditor.address,
      status: creditor.status,
      representative: creditor.representative,
      nit: creditor.nit,
      website: creditor.website,
      city: creditor.city,
      department: creditor.department,
      bankName: creditor.bankName,
      activeCases: creditor.activeCases,
      createdDate: creditor.createdDate,
      lastUpdate: creditor.lastUpdate,
      description: creditor.description,
      representativeId: creditor.representativeId,
      representativeEmail: creditor.representativeEmail,
      representativePhone: creditor.representativePhone,
      contacts: creditor.contacts,
      debts: creditor.debts.map((debt) => ({
        id: debt.id,
        amount: convertDecimalToNumber(debt.amount),
        interestRate: debt.interestRate,
        type: debt.type,
        caseId: debt.caseId,
        creditorId: debt.creditorId,
        debtorId: debt.debtorId,
        case: debt.case,
      })),
      _count: creditor._count,
    };
  } catch (error) {
    console.error('Error fetching creditor:', error);
    throw new Error('Error al obtener el acreedor');
  }
}

export async function createCreditor(
  values: CreateCreditorData,
): Promise<ActionResult<PrismaCreditor>> {
  const { success, data, error } = createCreditorSchema.safeParse(values);

  if (!success) {
    return {
      success: false,
      message: 'Por favor complete todos los campos requeridos',
      errors: error.flatten().fieldErrors,
    };
  }

  try {
    const creditor = await prisma.creditor.create({
      data: {
        ...data,
        status: 'Activo',
      },
      include: {
        contacts: true,
        debts: true,
        _count: {
          select: {
            debts: true,
          },
        },
      },
    });

    revalidatePath('/creditors');

    return {
      success: true,
      message: 'Acreedor creado exitosamente',
      data: creditor,
    };
  } catch (error) {
    console.error('Error creating creditor:', error);
    return {
      success: false,
      message: 'Error al crear el acreedor',
    };
  }
}

export async function updateCreditor(
  values: UpdateCreditorData,
): Promise<ActionResult<PrismaCreditor>> {
  const { success, data, error } = updateCreditorSchema.safeParse(values);

  if (!success) {
    return {
      success: false,
      message: 'Por favor complete todos los campos requeridos',
      errors: error.flatten().fieldErrors,
    };
  }

  try {
    const updatedCreditor = await prisma.creditor.update({
      where: { id: data.id },
      data,
      include: {
        contacts: true,
        debts: true,
        _count: {
          select: {
            debts: true,
          },
        },
      },
    });

    revalidatePath('/creditors');

    return {
      success: true,
      message: 'Acreedor actualizado exitosamente',
      data: updatedCreditor,
    };
  } catch (error) {
    console.error('Error updating creditor:', error);
    return {
      success: false,
      message: 'Error al actualizar el acreedor',
    };
  }
}

export async function deleteCreditor(
  id: string,
): Promise<ActionResult<PrismaCreditor>> {
  if (!id) {
    return {
      success: false,
      message: 'ID del acreedor es requerido',
    };
  }

  try {
    const deletedCreditor = await prisma.creditor.delete({
      where: { id },
    });

    revalidatePath('/creditors');

    return {
      success: true,
      message: 'Acreedor eliminado exitosamente',
      data: deletedCreditor,
    };
  } catch (error) {
    console.error('Error deleting creditor:', error);
    return {
      success: false,
      message: 'Error al eliminar el acreedor',
    };
  }
}

export async function addContact(
  creditorId: string,
  values: CreateContactData,
): Promise<ActionResult<PrismaContact>> {
  const { success, data, error } = createContactSchema.safeParse(values);

  if (!success) {
    return {
      success: false,
      message: 'Por favor complete todos los campos requeridos',
      errors: error.flatten().fieldErrors,
    };
  }

  try {
    const contact = await prisma.contact.create({
      data: {
        ...data,
        creditorId,
      },
    });

    revalidatePath('/creditors');

    return {
      success: true,
      message: 'Contacto agregado exitosamente',
      data: contact,
    };
  } catch (error) {
    console.error('Error adding contact:', error);
    return {
      success: false,
      message: 'Error al agregar el contacto',
    };
  }
}

export async function removeContact(contactId: string) {
  try {
    if (!contactId) {
      throw new Error('ID del contacto es requerido');
    }

    await prisma.contact.delete({
      where: { id: contactId },
    });

    revalidatePath('/creditors');

    return {
      success: true,
      message: 'Contacto eliminado exitosamente',
    };
  } catch (error) {
    console.error('Error removing contact:', error);
    return {
      success: false,
      message: 'Error al eliminar el contacto',
    };
  }
}

export async function toggleCreditorStatus(id: string) {
  try {
    if (!id) {
      throw new Error('ID del acreedor es requerido');
    }

    const creditor = await prisma.creditor.findUnique({
      where: { id },
      select: { status: true },
    });

    if (!creditor) {
      throw new Error('Acreedor no encontrado');
    }

    const newStatus = creditor.status === 'Activo' ? 'Inactivo' : 'Activo';

    await prisma.creditor.update({
      where: { id },
      data: { status: newStatus },
    });

    revalidatePath('/creditors');

    return {
      success: true,
      message: `Estado del acreedor actualizado a ${newStatus}`,
    };
  } catch (error) {
    console.error('Error toggling creditor status:', error);
    return {
      success: false,
      message: 'Error al cambiar el estado del acreedor',
    };
  }
}
