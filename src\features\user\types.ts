import type { User as PrismaUser, Role as PrismaRole } from '@prisma/client';

export type UserWithRole = PrismaUser & {
  role: PrismaRole;
  assignedCases: { id: string }[];
};

export type UIUser = Omit<UserWithRole, 'lastLogin' | 'createdDate'> & {
  lastLogin: string;
  createdDate: string;
  casesAssigned: number;
  permissions: string[];
};

export type UIRole = PrismaRole & {
  users: number;
};
