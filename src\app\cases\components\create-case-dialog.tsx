'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { createCaseAction, updateCase } from '@/features/case/actions';
import {
  type Asset,
  type Debt,
  type LegalProcess,
  type CaseData,
} from '@/features/case/types';
import { getCreditors } from '@/features/creditor/actions';
import { getDebtors, createDebtor } from '@/features/debtor/actions';
import { getAllUsers } from '@/features/user/actions';

import { AssetsSection } from './create-case-dialog/assets-section';
import { CreateDebtorDialog } from './create-case-dialog/create-debtor-dialog';
import { CreditorsSection } from './create-case-dialog/creditors-section';
import { DebtorSection } from './create-case-dialog/debtor-section';
import { DocumentsSection } from './create-case-dialog/documents-section';

interface CreateCaseDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCaseCreated: () => void;
  editMode?: boolean;
  caseData?: CaseData;
}

interface DebtorOption {
  id: string;
  name: string;
  idNumber: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  department: string;
  monthlyIncome: number;
  monthlyExpenses: number;
}

interface CreditorOption {
  id: string;
  name: string;
  nit: string;
  type: string;
}

interface NewDebtForm {
  creditor: string;
  creditorId: string | undefined;
  amount: string;
  type: string;
  interestRate: string;
}

export function CreateCaseDialog({
  open,
  onOpenChange,
  onCaseCreated,
  editMode = false,
  caseData,
}: Readonly<CreateCaseDialogProps>) {
  const [loading, setLoading] = useState(false);
  const [dataLoading, setDataLoading] = useState(false);
  const [debtors, setDebtors] = useState<DebtorOption[]>([]);
  const [creditors, setCreditors] = useState<CreditorOption[]>([]);
  const [selectedDebtorId, setSelectedDebtorId] = useState<string>('');
  const [selectedDebtor, setSelectedDebtor] = useState<DebtorOption | null>(
    null,
  );
  const [showCreateDebtorDialog, setShowCreateDebtorDialog] = useState(false);
  const [newDebtorForm, setNewDebtorForm] = useState({
    name: '',
    idNumber: '',
    email: '',
    phone: '',
    address: '',
    city: 'Bogotá',
    department: 'Cundinamarca',
    monthlyIncome: 0,
    monthlyExpenses: 0,
  });
  const [creatingDebtor, setCreatingDebtor] = useState(false);

  const [formData, setFormData] = useState({
    debtorName: '',
    debtorId: '',
    debtorEmail: '',
    debtorPhone: '',
    debtorAddress: '',
    caseType: '',
    totalDebt: '',
    monthlyIncome: '',
    monthlyExpenses: '',
    operatorId: '',
    causes: [] as string[],
    debts: [] as Debt[],
    assets: [] as Asset[],
    legalProcesses: [] as LegalProcess[],
  });

  useEffect(() => {
    if (open) {
      const loadData = async () => {
        setDataLoading(true);
        try {
          const [debtorsData, creditorsData, usersData] = await Promise.all([
            getDebtors(),
            getCreditors(),
            getAllUsers(),
          ]);
          setDebtors(debtorsData as DebtorOption[]);
          setCreditors(creditorsData as CreditorOption[]);

          const operatorUser = usersData.find(
            (user) =>
              user.role?.name === 'Operadora de Insolvencia' ||
              user.role?.name === 'Abogado',
          );
          if (operatorUser) {
            setFormData((prev) => ({ ...prev, operatorId: operatorUser.id }));
          }
        } catch {
          toast.error('Error al cargar los datos');
        } finally {
          setDataLoading(false);
        }
      };
      void loadData();
    }
  }, [open]);

  useEffect(() => {
    if (!showCreateDebtorDialog && open) {
      const reloadDebtors = async () => {
        try {
          const debtorsData = await getDebtors();
          setDebtors(debtorsData as DebtorOption[]);
        } catch {}
      };
      void reloadDebtors();
    }
  }, [showCreateDebtorDialog, open]);

  useEffect(() => {
    if (selectedDebtorId && debtors.length > 0) {
      const debtor = debtors.find((d) => d.id === selectedDebtorId);
      if (debtor) {
        setSelectedDebtor(debtor);
        setFormData((prev) => ({
          ...prev,
          debtorName: debtor.name,
          debtorId: debtor.idNumber,
          debtorEmail: debtor.email,
          debtorPhone: debtor.phone,
          debtorAddress: `${debtor.address}, ${debtor.city}, ${debtor.department}`,
          monthlyIncome: debtor.monthlyIncome.toString(),
          monthlyExpenses: debtor.monthlyExpenses.toString(),
        }));
      }
    }
  }, [selectedDebtorId, debtors]);

  const [newDebt, setNewDebt] = useState<NewDebtForm>({
    creditor: '',
    creditorId: undefined,
    amount: '',
    type: '',
    interestRate: '',
  });

  const insolvencyCauses = [
    'Sobreendeudamiento por falta de recursos',
    'Pérdida de empleo',
    'Divorcio',
    'Enfermedad',
    'Accidente',
    'Estafa',
    'Disminución de ingresos',
    'Falta de educación financiera',
    'Muerte de cónyuge',
    'Nuevos créditos para cubrir otros',
    'Mala inversión',
  ];

  const handleCauseChange = (cause: string) => {
    setFormData((prev) => {
      const newCauses = prev.causes.includes(cause)
        ? prev.causes.filter((c) => c !== cause)
        : [...prev.causes, cause];
      return { ...prev, causes: newCauses };
    });
  };

  const addDebt = () => {
    if (newDebt.creditor && newDebt.amount) {
      setFormData((prev) => ({
        ...prev,
        debts: [
          ...prev.debts,
          {
            ...newDebt,
            id: Date.now().toString(),
            amount: Number(newDebt.amount),
            interestRate: Number(newDebt.interestRate),
          },
        ],
      }));
      setNewDebt({
        creditor: '',
        creditorId: undefined,
        amount: '',
        type: '',
        interestRate: '',
      });
    }
  };

  const removeDebt = (debtId: string) => {
    setFormData((prev) => ({
      ...prev,
      debts: prev.debts.filter((debt) => debt.id !== debtId),
    }));
  };

  const handleSubmit = async () => {
    if (!selectedDebtorId) {
      toast.error('Por favor seleccione un deudor');
      return;
    }

    if (!formData.caseType) {
      toast.error('Por favor seleccione el tipo de caso');
      return;
    }

    if (!formData.totalDebt || Number(formData.totalDebt) <= 0) {
      toast.error('Por favor ingrese el monto total de la deuda');
      return;
    }

    setLoading(true);
    try {
      const { success, message } =
        editMode && caseData
          ? await updateCase({
              id: caseData.id,
              debtorId: selectedDebtorId,
              type: formData.caseType as
                | 'INSOLVENCY'
                | 'CONCILIATION'
                | 'SUPPORT_AGREEMENT',
              status: caseData.status,
              totalDebt: Number(formData.totalDebt),
              creditors: formData.debts.length || 1,
              operatorId: caseData.operatorId,
              phase: caseData.phase ?? undefined,
            })
          : await createCaseAction({
              debtorId: selectedDebtorId,
              type: formData.caseType as
                | 'INSOLVENCY'
                | 'CONCILIATION'
                | 'SUPPORT_AGREEMENT',
              status: 'NEGOTIATION',
              totalDebt: Number(formData.totalDebt),
              creditors: formData.debts.length || 1,
              operatorId: formData.operatorId || 'clm1234567890', // Fallback to default if not set
              phase: 'En negociación',
              causes: formData.causes,
            });

      if (success) {
        toast.success(message);
        onOpenChange(false);
        setSelectedDebtorId('');
        setSelectedDebtor(null);
        setFormData({
          debtorName: '',
          debtorId: '',
          debtorEmail: '',
          debtorPhone: '',
          debtorAddress: '',
          caseType: '',
          totalDebt: '',
          monthlyIncome: '',
          monthlyExpenses: '',
          operatorId: '',
          causes: [],
          debts: [],
          assets: [],
          legalProcesses: [],
        });
        onCaseCreated();
      } else {
        toast.error(message);
      }
    } catch {
      toast.error('Error al crear el caso');
    } finally {
      setLoading(false);
    }
  };

  const getButtonText = () => {
    if (loading) return 'Procesando...';
    return editMode ? 'Guardar Cambios' : 'Crear Caso';
  };

  const handleCreateDebtor = async () => {
    if (
      !newDebtorForm.name ||
      !newDebtorForm.idNumber ||
      !newDebtorForm.email ||
      !newDebtorForm.phone
    ) {
      toast.error('Por favor complete todos los campos requeridos');
      return;
    }

    setCreatingDebtor(true);
    try {
      const { success, message, data } = await createDebtor({
        ...newDebtorForm,
        idType: 'CC',
        occupation: 'Empleado',
        dependents: 0,
        maritalStatus: 'SOLTERO',
        educationLevel: 'UNIVERSITARIO',
        emergencyContact: '',
        emergencyPhone: '',
        bankAccount: '',
        bankName: '',
        accountType: 'AHORROS',
        description: '',
      });

      if (success) {
        toast.success('Deudor creado exitosamente');
        setShowCreateDebtorDialog(false);
        setNewDebtorForm({
          name: '',
          idNumber: '',
          email: '',
          phone: '',
          address: '',
          city: 'Bogotá',
          department: 'Cundinamarca',
          monthlyIncome: 0,
          monthlyExpenses: 0,
        });
        const debtorsData = await getDebtors();
        setDebtors(debtorsData as DebtorOption[]);
        if (data?.id) {
          setSelectedDebtorId(data.id);
        }
      } else {
        toast.error(message);
      }
    } catch {
      toast.error('Error al crear el deudor');
    } finally {
      setCreatingDebtor(false);
    }
  };

  useEffect(() => {
    if (!open) {
      setSelectedDebtorId('');
      setSelectedDebtor(null);
      setFormData({
        debtorName: '',
        debtorId: '',
        debtorEmail: '',
        debtorPhone: '',
        debtorAddress: '',
        caseType: '',
        totalDebt: '',
        monthlyIncome: '',
        monthlyExpenses: '',
        operatorId: '',
        causes: [],
        debts: [],
        assets: [],
        legalProcesses: [],
      });
      setNewDebtorForm({
        name: '',
        idNumber: '',
        email: '',
        phone: '',
        address: '',
        city: 'Bogotá',
        department: 'Cundinamarca',
        monthlyIncome: 0,
        monthlyExpenses: 0,
      });
    }
  }, [open]);

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-6xl">
          <DialogHeader>
            <DialogTitle>
              {editMode ? `Editar Caso ${caseData?.id}` : 'Crear Nuevo Caso'}
            </DialogTitle>
            <DialogDescription>
              {editMode
                ? `Modifique la información del caso de ${caseData?.debtorName}`
                : 'Complete la información para crear un nuevo caso de insolvencia'}
            </DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="debtor" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="debtor">Deudor</TabsTrigger>
              <TabsTrigger value="creditors">Acreedores</TabsTrigger>
              <TabsTrigger value="assets">Bienes</TabsTrigger>
              <TabsTrigger value="documents">Documentos</TabsTrigger>
            </TabsList>

            <TabsContent value="debtor" className="space-y-4">
              <DebtorSection
                debtors={debtors}
                selectedDebtorId={selectedDebtorId}
                selectedDebtor={selectedDebtor}
                formData={{
                  caseType: formData.caseType,
                  totalDebt: formData.totalDebt,
                  causes: formData.causes,
                }}
                insolvencyCauses={insolvencyCauses}
                onDebtorSelect={setSelectedDebtorId}
                onCreateDebtorClick={() => setShowCreateDebtorDialog(true)}
                onFormDataChange={(data) =>
                  setFormData((prev) => ({ ...prev, ...data }))
                }
                onCauseChange={handleCauseChange}
                loading={dataLoading}
              />
            </TabsContent>

            <TabsContent value="creditors" className="space-y-4">
              <CreditorsSection
                creditors={creditors}
                newDebt={newDebt}
                debts={formData.debts}
                onNewDebtChange={(debt) =>
                  setNewDebt({
                    creditor: debt.creditor,
                    creditorId: debt.creditorId ?? undefined,
                    amount: debt.amount,
                    type: debt.type,
                    interestRate: debt.interestRate,
                  })
                }
                onAddDebt={addDebt}
                onRemoveDebt={removeDebt}
              />
            </TabsContent>

            <TabsContent value="assets" className="space-y-4">
              <AssetsSection />
            </TabsContent>

            <TabsContent value="documents" className="space-y-4">
              <DocumentsSection />
            </TabsContent>
          </Tabs>
          <DialogFooter>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancelar
            </Button>
            <Button onClick={() => void handleSubmit()} disabled={loading}>
              {getButtonText()}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <CreateDebtorDialog
        open={showCreateDebtorDialog}
        onOpenChange={setShowCreateDebtorDialog}
        newDebtorForm={newDebtorForm}
        creatingDebtor={creatingDebtor}
        onFormChange={setNewDebtorForm}
        onSubmit={handleCreateDebtor}
      />
    </>
  );
}
