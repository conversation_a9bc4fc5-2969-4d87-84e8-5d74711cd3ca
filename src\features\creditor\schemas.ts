import { z } from 'zod';

export const createCreditorSchema = z.object({
  name: z.string().min(1, 'El nombre es requerido'),
  type: z.string().min(1, 'El tipo es requerido'),
  email: z.string().email('Email inválido'),
  phone: z.string().min(1, 'El teléfono es requerido'),
  address: z.string().min(1, 'La dirección es requerida'),
  city: z.string().optional(),
  department: z.string().optional(),
  representative: z.string().min(1, 'El representante es requerido'),
  nit: z.string().min(1, 'El NIT es requerido'),
  website: z.string().optional(),
  description: z.string().optional(),
  representativeEmail: z
    .string()
    .email('Email del representante inválido')
    .optional(),
  representativeId: z.string().optional(),
  representativePhone: z.string().optional(),
  bankName: z.string().optional(),
});

export const updateCreditorFormSchema = z.object({
  name: z.string().min(1, 'El nombre es requerido').optional(),
  type: z.string().min(1, 'El tipo es requerido').optional(),
  email: z.string().email('Email inválido').optional(),
  phone: z.string().min(1, 'El teléfono es requerido').optional(),
  address: z.string().min(1, 'La dirección es requerida').optional(),
  city: z.string().optional(),
  department: z.string().optional(),
  representative: z.string().min(1, 'El representante es requerido').optional(),
  nit: z.string().min(1, 'El NIT es requerido').optional(),
  website: z.string().optional(),
  description: z.string().optional(),
  representativeEmail: z
    .string()
    .email('Email del representante inválido')
    .optional(),
  representativeId: z.string().optional(),
  representativePhone: z.string().optional(),
  bankName: z.string().optional(),
});

export const updateCreditorSchema = z.object({
  id: z.string().min(1, 'El ID es requerido'),
  name: z.string().min(1, 'El nombre es requerido').optional(),
  type: z.string().min(1, 'El tipo es requerido').optional(),
  email: z.string().email('Email inválido').optional(),
  phone: z.string().min(1, 'El teléfono es requerido').optional(),
  address: z.string().min(1, 'La dirección es requerida').optional(),
  city: z.string().optional(),
  department: z.string().optional(),
  representative: z.string().min(1, 'El representante es requerido').optional(),
  nit: z.string().min(1, 'El NIT es requerido').optional(),
  website: z.string().optional(),
  description: z.string().optional(),
  representativeEmail: z
    .string()
    .email('Email del representante inválido')
    .optional(),
  representativeId: z.string().optional(),
  representativePhone: z.string().optional(),
  bankName: z.string().optional(),
});

export const createContactSchema = z.object({
  name: z.string().min(1, 'El nombre es requerido'),
  email: z.string().email('Email inválido'),
  phone: z.string().min(1, 'El teléfono es requerido'),
  role: z.string().min(1, 'El rol es requerido'),
});
