'use server';

import { revalidateTag } from 'next/cache';

import prisma from '@/lib/prisma';

import { createUserSchema, updateUserSchema } from './schemas';

import type { CreateUserData, UpdateUserData } from './schemas';
import type { UserWithRole, UIRole } from './types';
import type { ActionResult } from '@/lib/types';
import type { User as PrismaUser } from '@prisma/client';

export async function getAllUsers(): Promise<UserWithRole[]> {
  return await prisma.user.findMany({
    include: {
      role: true,
      assignedCases: {
        select: {
          id: true,
        },
      },
    },
    orderBy: {
      name: 'asc',
    },
  });
}

export async function getAllRoles(): Promise<UIRole[]> {
  const roles = await prisma.role.findMany({
    include: {
      _count: {
        select: {
          users: true,
        },
      },
    },
  });

  return roles.map((role) => ({
    ...role,
    users: role._count.users,
  }));
}

export async function createUser(
  values: CreateUserData,
): Promise<ActionResult<PrismaUser>> {
  'use server';
  const { success, data, error } = createUserSchema.safeParse(values);

  if (!success) {
    return {
      success: false,
      message: 'Por favor complete todos los campos requeridos',
      errors: error.flatten().fieldErrors,
    };
  }

  try {
    const { roleId, ...rest } = data;
    const createdUser = await prisma.user.create({
      data: {
        ...rest,
        role: {
          connect: { id: roleId },
        },
      },
    });

    revalidateTag('users');

    return {
      success: true,
      message: 'Usuario creado exitosamente',
      data: createdUser,
    };
  } catch (error) {
    console.error('Error creating user:', error);
    return {
      success: false,
      message: 'Error al crear el usuario',
    };
  }
}

export async function updateUser(
  values: UpdateUserData,
): Promise<ActionResult<PrismaUser>> {
  'use server';
  const { success, data, error } = updateUserSchema.safeParse(values);

  if (!success) {
    return {
      success: false,
      message: 'Por favor complete todos los campos requeridos',
      errors: error.flatten().fieldErrors,
    };
  }

  try {
    const { id, roleId, ...rest } = data;
    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        ...rest,
        ...(roleId && {
          role: {
            connect: { id: roleId },
          },
        }),
      },
    });

    revalidateTag('users');

    return {
      success: true,
      message: 'Usuario actualizado exitosamente',
      data: updatedUser,
    };
  } catch (error) {
    console.error('Error updating user:', error);
    return {
      success: false,
      message: 'Error al actualizar el usuario',
    };
  }
}

export async function deleteUser(
  id: string,
): Promise<ActionResult<PrismaUser>> {
  'use server';
  if (!id) {
    return {
      success: false,
      message: 'ID del usuario es requerido',
    };
  }

  try {
    const deletedUser = await prisma.user.delete({
      where: { id },
    });

    revalidateTag('users');

    return {
      success: true,
      message: 'Usuario eliminado exitosamente',
      data: deletedUser,
    };
  } catch (error) {
    console.error('Error deleting user:', error);
    return {
      success: false,
      message: 'Error al eliminar el usuario',
    };
  }
}
