'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import {
  User as UserIcon,
  Mail,
  Phone,
  MapPin,
  Shield,
  CreditCard,
  Loader2,
} from 'lucide-react';
import { useEffect, useRef, useTransition } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { updateUser } from '@/features/user/actions';
import { editUserFormSchema } from '@/features/user/schemas';
import { getPermissionName } from '@/lib/permissions';

import type { EditUserFormData, UIUser, UIRole } from '@/features/user/types';

interface EditUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: UIUser;
  onUserUpdated?: () => void;
  roles: UIRole[];
}

export function EditUserDialog({
  open,
  onOpenChange,
  user,
  onUserUpdated,
  roles,
}: Readonly<EditUserDialogProps>) {
  const [isPending, startTransition] = useTransition();
  const closeRef = useRef<HTMLButtonElement>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
    setError,
    clearErrors,
  } = useForm<EditUserFormData>({
    resolver: zodResolver(editUserFormSchema),
    defaultValues: {
      name: user?.name ?? '',
      email: user?.email ?? '',
      phone: user?.phone ?? '',
      address: user?.address ?? '',
      roleId: user?.roleId ?? '',
      professionalCard: user?.professionalCard ?? '',
      status: user?.status ?? '',
    },
  });

  useEffect(() => {
    if (user) {
      reset({
        name: user?.name ?? '',
        email: user?.email ?? '',
        phone: user?.phone ?? '',
        address: user?.address ?? '',
        roleId: user?.roleId ?? '',
        professionalCard: user?.professionalCard ?? '',
        status: user?.status ?? 'Activo',
      });
    }
  }, [user, reset]);

  const watchedRoleId = watch('roleId');
  const selectedRole = roles.find((r) => r.id === watchedRoleId);

  const onSubmit = (data: EditUserFormData) => {
    startTransition(async () => {
      const { success, message, errors } = await updateUser({
        id: user.id,
        ...data,
      });

      if (success) {
        clearErrors();
        toast.success('Usuario actualizado exitosamente', {
          description: `El usuario ${data.name} ha sido actualizado correctamente`,
        });
        closeRef.current?.click();
        onUserUpdated?.();
      } else {
        setError('root.serverError', { message });

        if (errors) {
          Object.entries(errors).forEach(([field, messages]) => {
            if (messages.length > 0) {
              setError(field as keyof EditUserFormData, {
                message: messages[0],
              });
            }
          });
        }
      }
    });
  };

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle>Editar Usuario</DialogTitle>
          <DialogDescription>
            Modifique la información del usuario {user?.name ?? 'N/A'}
          </DialogDescription>
        </DialogHeader>

        <form
          onSubmit={(e) => {
            e.preventDefault();
            void handleSubmit(onSubmit)();
          }}
          className="space-y-4"
        >
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name">Nombre Completo</Label>
              <div className="relative">
                <UserIcon className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                <Input
                  id="name"
                  {...register('name')}
                  className={`pl-10 ${errors.name ? 'border-red-500' : ''}`}
                />
              </div>
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Correo Electrónico</Label>
              <div className="relative">
                <Mail className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                <Input
                  id="email"
                  type="email"
                  {...register('email')}
                  className={`pl-10 ${errors.email ? 'border-red-500' : ''}`}
                />
              </div>
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="phone">Teléfono</Label>
              <div className="relative">
                <Phone className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                <Input id="phone" {...register('phone')} className="pl-10" />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="professionalCard">Tarjeta Profesional</Label>
              <div className="relative">
                <CreditCard className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                <Input
                  id="professionalCard"
                  {...register('professionalCard')}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="address">Dirección</Label>
            <div className="relative">
              <MapPin className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
              <Input id="address" {...register('address')} className="pl-10" />
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="role">Rol</Label>
              <div className="relative">
                <Shield className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                <Select
                  value={watch('roleId')}
                  onValueChange={(value) => setValue('roleId', value)}
                >
                  <SelectTrigger
                    className={`pl-10 ${errors.roleId ? 'border-red-500' : ''}`}
                  >
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {roles.map((role) => (
                      <SelectItem key={role?.id ?? ''} value={role?.id ?? ''}>
                        {role?.name ?? 'N/A'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {errors.roleId && (
                <p className="text-sm text-red-500">{errors.roleId.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Estado</Label>
              <Select
                value={watch('status')}
                onValueChange={(value) => setValue('status', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Activo">Activo</SelectItem>
                  <SelectItem value="Inactivo">Inactivo</SelectItem>
                  <SelectItem value="Suspendido">Suspendido</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {selectedRole && (
            <div className="rounded-lg border bg-green-50 p-4">
              <h3 className="mb-2 font-medium">
                Permisos del Rol: {selectedRole?.name ?? 'N/A'}
              </h3>
              <ul className="space-y-1 text-sm text-gray-600">
                {(selectedRole?.permissions ?? []).map((permission: string) => (
                  <li key={permission} className="flex items-center">
                    <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
                    {getPermissionName(permission)}
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="rounded-lg border bg-blue-50 p-4">
            <h3 className="mb-2 font-medium">Información del Sistema</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600">Casos Asignados:</p>
                <p className="font-medium">
                  {user?.assignedCases?.length ?? 0}
                </p>
              </div>
              <div>
                <p className="text-gray-600">Último Acceso:</p>
                <p className="font-medium">
                  {!user?.lastLogin
                    ? 'Nunca'
                    : new Date(user.lastLogin).toLocaleString('es-CO')}
                </p>
              </div>
              <div>
                <p className="text-gray-600">Fecha de Creación:</p>
                <p className="font-medium">
                  {user?.createdDate
                    ? new Date(user.createdDate).toLocaleDateString('es-CO')
                    : 'N/A'}
                </p>
              </div>
            </div>
          </div>

          <DialogFooter>
            <DialogClose asChild>
              <Button ref={closeRef} variant="outline" type="button">
                Cancelar
              </Button>
            </DialogClose>
            <Button type="submit" disabled={isPending}>
              {isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Guardando...
                </>
              ) : (
                'Guardar Cambios'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
